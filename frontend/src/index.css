:root {
  /* Professional Typography */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Can<PERSON><PERSON>', '<PERSON>ra Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* Color Scheme */
  color-scheme: light dark;
  color: #2c3e50;
  background-color: #f8fafc;

  /* Blue Gradient Theme Variables */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --blue-gradient-light: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #6c5ce7 100%);
  --blue-gradient-dark: linear-gradient(135deg, #2d3748 0%, #4a5568 50%, #2b6cb0 100%);
  --accent-blue: #3182ce;
  --accent-blue-light: #63b3ed;
  --accent-blue-dark: #2c5282;

  /* Professional Color Palette */
  --text-primary: #2d3748;
  --text-secondary: #4a5568;
  --text-muted: #718096;
  --bg-primary: #ffffff;
  --bg-secondary: #f7fafc;
  --bg-tertiary: #edf2f7;
  --border-color: #e2e8f0;
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Typography Scale */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Spacing Scale */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);

  /* Font Rendering */
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Global Reset and Base Styles */
* {
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  height: 100%;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  height: 100%;
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--primary-gradient);
  background-attachment: fixed;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

#root {
  min-height: 100vh;
  position: relative;
}

/* Professional Link Styles */
a {
  color: var(--accent-blue);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition-fast);
}

a:hover {
  color: var(--accent-blue-dark);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--accent-blue-light);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Professional Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0 0 var(--spacing-4) 0;
  font-weight: 700;
  line-height: 1.2;
  color: var(--text-primary);
  letter-spacing: -0.025em;
}

h1 {
  font-size: var(--font-size-4xl);
  font-weight: 800;
}

h2 {
  font-size: var(--font-size-3xl);
  font-weight: 700;
}

h3 {
  font-size: var(--font-size-2xl);
  font-weight: 600;
}

h4 {
  font-size: var(--font-size-xl);
  font-weight: 600;
}

h5 {
  font-size: var(--font-size-lg);
  font-weight: 600;
}

h6 {
  font-size: var(--font-size-base);
  font-weight: 600;
}

p {
  margin: 0 0 var(--spacing-4) 0;
  color: var(--text-secondary);
}

/* Professional Button Base Styles */
button {
  border-radius: var(--radius-lg);
  border: 1px solid transparent;
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-base);
  font-weight: 600;
  font-family: inherit;
  background-color: var(--accent-blue);
  color: white;
  cursor: pointer;
  transition: var(--transition-base);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  text-decoration: none;
  box-shadow: var(--shadow-sm);
}

button:hover:not(:disabled) {
  background-color: var(--accent-blue-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

button:focus,
button:focus-visible {
  outline: 2px solid var(--accent-blue-light);
  outline-offset: 2px;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Professional Code Styling */
code {
  font-family: 'JetBrains Mono', 'Fira Code', 'Cascadia Code', 'SF Mono', Monaco, 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;
  background-color: var(--bg-tertiary);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: 0.875em;
  color: var(--accent-blue-dark);
}

/* Professional Form Controls */
.form-control,
.form-select {
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-base);
  font-weight: 500;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: var(--transition-base);
  box-shadow: var(--shadow-sm);
}

.form-control:focus,
.form-select:focus {
  border-color: var(--accent-blue);
  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
  outline: none;
  transform: translateY(-1px);
}

.form-control:hover:not(:disabled),
.form-select:hover:not(:disabled) {
  border-color: var(--accent-blue-light);
  box-shadow: var(--shadow-md);
}

/* Professional Card Styling */
.card {
  border: none;
  border-radius: var(--radius-2xl);
  background-color: var(--bg-primary);
  box-shadow: var(--shadow-lg);
  transition: var(--transition-base);
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.95);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.card-header {
  border-bottom: 1px solid var(--border-color);
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0 !important;
  background: var(--blue-gradient-light);
  color: white;
  padding: var(--spacing-6);
}

.card-body {
  padding: var(--spacing-8);
}

/* Professional Table Styling */
.table {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  background-color: var(--bg-primary);
}

.table th {
  background: var(--blue-gradient-light);
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  font-size: var(--font-size-sm);
  letter-spacing: 0.05em;
  padding: var(--spacing-4) var(--spacing-6);
  border: none;
}

.table td {
  padding: var(--spacing-4) var(--spacing-6);
  border-color: var(--border-color);
  vertical-align: middle;
}

.table tbody tr {
  transition: var(--transition-fast);
}

.table tbody tr:hover {
  background-color: rgba(49, 130, 206, 0.05);
  transform: scale(1.005);
}

/* Professional Button Variants */
.btn-outline-primary {
  border: 2px solid var(--accent-blue);
  color: var(--accent-blue);
  background-color: transparent;
}

.btn-outline-primary:hover {
  background-color: var(--accent-blue);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Professional Responsive Design */
@media (max-width: 768px) {
  :root {
    --font-size-4xl: 2rem;
    --font-size-3xl: 1.75rem;
    --font-size-2xl: 1.5rem;
    --spacing-8: 1.5rem;
    --spacing-6: 1.25rem;
  }

  .container {
    padding-left: var(--spacing-4);
    padding-right: var(--spacing-4);
  }

  .card {
    margin-left: calc(-1 * var(--spacing-4));
    margin-right: calc(-1 * var(--spacing-4));
    border-radius: var(--radius-xl);
  }

  .card-body {
    padding: var(--spacing-6);
  }

  .card-header {
    padding: var(--spacing-5);
  }
}

@media (max-width: 480px) {
  .card-body {
    padding: var(--spacing-4);
  }

  .card-header {
    padding: var(--spacing-4);
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #f7fafc;
    --text-secondary: #e2e8f0;
    --text-muted: #a0aec0;
    --bg-primary: rgba(45, 55, 72, 0.95);
    --bg-secondary: #2d3748;
    --bg-tertiary: #4a5568;
    --border-color: #4a5568;
    color: var(--text-primary);
    background: var(--blue-gradient-dark);
  }

  .card {
    background-color: var(--bg-primary);
  }

  button {
    background-color: var(--accent-blue);
  }

  a:hover {
    color: var(--accent-blue-light);
  }
}