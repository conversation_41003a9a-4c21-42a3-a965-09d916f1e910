html {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  background: linear-gradient(135deg, #1a237e 0%, #283593 50%, #3949ab 100%);
  background-attachment: fixed;
}

body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #1a237e 0%, #283593 50%, #3949ab 100%);
  background-attachment: fixed;
}

#root {
  margin: 0;
  padding: 0;
  width: 100%;
  min-height: 100vh;
  position: relative;
  background: transparent;
}

/* Container styling */
.container {
  position: relative;
  padding: 2rem 1rem;
}

/* Center the form content */
.form-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  width: 100%;
  padding: 2rem 0;
  position: relative;
  background: transparent;
}

/* Adjust card backgrounds for better contrast */
.card {
  background-color: #ffffff;
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border: none;
  border-radius: 12px;
}

.dark-theme .card {
  background-color: #2a2a2a;
}

@media (min-width: 768px) {
  .container {
    padding: 2rem;
  }
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}

.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}

.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.read-the-docs {
  color: #888;
}

.dark-theme {
  color: #ffffff;
  min-height: 100vh;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  background-attachment: fixed;
}

.dark-theme html,
.dark-theme body,
.dark-theme #root,
.dark-theme .form-container {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  background-attachment: fixed;
}

.reconciliation-form {
  background-color: #ffffff;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

@media (min-width: 768px) {
  .reconciliation-form {
    padding: 2.5rem;
  }
}

.dark-theme .form-control,
.dark-theme .form-select {
  background-color: #2d2d2d;
  border-color: #3d3d3d;
  color: #ffffff;
}

.dark-theme .form-control:focus,
.dark-theme .form-select:focus {
  background-color: #2d2d2d;
  border-color: #4d4d4d;
  color: #ffffff;
  box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.25);
}

.dark-theme .form-label {
  color: #ffffff;
}

.dark-theme .modal-content {
  background-color: #2d2d2d;
  color: #ffffff;
}

.dark-theme .modal-header {
  border-bottom-color: #3d3d3d;
}

.dark-theme .modal-footer {
  border-top-color: #3d3d3d;
}

.dark-theme .btn-outline-light:hover {
  background-color: #ffffff;
  color: #1a1a1a;
}

.dark-theme a {
  color: #4dabf7;
}

.dark-theme a:hover {
  color: #74c0fc;
}

.dark-theme .alert-danger {
  background-color: #842029;
  border-color: #842029;
  color: #ffffff;
}

.dark-theme .table {
  color: #ffffff;
}

.dark-theme .table td,
.dark-theme .table th {
  border-color: #3d3d3d;
}

.dark-theme .table-striped>tbody>tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05);
}

.dark-theme .table-hover>tbody>tr:hover {
  background-color: rgba(255, 255, 255, 0.075);
}

.stat-card {
  background-color: #ffffff;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
}

.stat-card h4 {
  color: #2c3e50;
  margin-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 10px;
}

.results-page {
  padding: 2rem 0;
}

.download-links {
  background-color: #ffffff;
  padding: 1.5rem;
  border-radius: 8px;
  margin-top: 2rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
}

.download-links h4 {
  color: #2c3e50;
  margin-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 10px;
}

.table {
  margin-bottom: 0;
}

.text-success {
  color: #28a745 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.text-warning {
  color: #ffc107 !important;
}

.table-dark {
  background-color: #2d2d2d;
}

.table-dark td,
.table-dark th {
  border-color: #444;
}

.btn-outline-light:hover {
  background-color: #444;
  color: #fff;
}

/* Form Sections */
.form-section {
  background: #ffffff;
  border-radius: 1rem;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-title {
  color: #2c3e50;
  font-weight: 700;
  font-size: 1.3rem;
  position: relative;
  padding-bottom: 0.75rem;
  margin-bottom: 1.5rem;
}

.section-title::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 60px;
  height: 4px;
  background: #0d6efd;
  border-radius: 2px;
}

/* Custom Form Controls */
.form-control-custom,
.form-select-custom {
  height: 3rem;
  border: 2px solid #e9ecef;
  border-radius: 0.75rem;
  padding: 0.5rem 0.75rem;
  font-size: 16px;
  /* Prevents zoom on iOS */
  background-color: #ffffff;
  transition: all 0.3s ease;
}

@media (min-width: 768px) {

  .form-control-custom,
  .form-select-custom {
    height: 3.5rem;
    padding: 0.75rem 1rem;
  }
}

.form-control-custom:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.1);
}

/* File Upload Cards */
.file-upload-card {
  background: #f8f9fa;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

@media (min-width: 768px) {
  .file-upload-card {
    padding: 2rem;
    margin-bottom: 1.5rem;
  }
}

.file-upload-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  border-color: #dee2e6;
}

.file-upload-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.25rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.file-icon {
  font-size: 1.75rem;
  color: #0d6efd;
}

.upload-box {
  border: 2px dashed #dee2e6;
  border-radius: 0.75rem;
  padding: 1.5rem 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #ffffff;
}

@media (min-width: 768px) {
  .upload-box {
    padding: 2.5rem 1.5rem;
  }
}

.upload-box:hover {
  border-color: #0d6efd;
  background-color: rgba(13, 110, 253, 0.05);
}

.upload-icon {
  font-size: 2.5rem;
  color: #0d6efd;
  display: block;
  margin-bottom: 1rem;
}

.upload-text {
  display: block;
  color: #495057;
  font-size: 1.1rem;
  margin-bottom: 0.75rem;
}

.selected-file {
  display: block;
  color: #0d6efd;
  font-size: 0.95rem;
  font-weight: 600;
  margin-top: 0.75rem;
  word-break: break-all;
  background-color: rgba(13, 110, 253, 0.1);
  padding: 0.5rem;
  border-radius: 0.5rem;
}

/* Options Card */
.options-card {
  background: #f8f9fa;
  border-radius: 1rem;
  padding: 1.75rem;
  border: 1px solid #e9ecef;
  margin-bottom: 2rem;
}

.custom-checkbox {
  font-size: 1.1rem;
  font-weight: 500;
}

/* Submit Button */
.submit-button {
  height: 3.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 1.1rem;
  padding: 0.75rem 1.5rem;
  width: 100%;
  background-color: #0d6efd;
  border-color: #0d6efd;
  box-shadow: 0 4px 6px rgba(13, 110, 253, 0.2);
}

@media (min-width: 768px) {
  .submit-button {
    height: 4rem;
    font-size: 1.2rem;
  }
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(13, 110, 253, 0.3);
  background-color: #0b5ed7;
  border-color: #0a58ca;
}

/* Results Page Styles */
.results-card {
  background: #ffffff;
  margin-bottom: 2.5rem;
  max-width: 1200px;
}

.back-button {
  padding: 0.75rem 1.25rem;
  font-weight: 600;
  border-radius: 0.75rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.back-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Stats section styling for results page */
.stats-section {
  margin-bottom: 3rem;
}

.stat-card {
  background: #f8f9fa;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

@media (min-width: 768px) {
  .stat-card {
    margin-bottom: 1.5rem;
    padding: 2rem;
  }
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  border-color: #dee2e6;
}

.stat-header {
  display: flex;
  align-items: center;
  gap: 1.25rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.stat-icon {
  font-size: 2rem;
  color: #0d6efd;
}

.stat-header h6 {
  margin: 0;
  font-weight: 700;
  font-size: 1.2rem;
  color: #2c3e50;
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  background: #ffffff;
  border-radius: 0.75rem;
  border: 1px solid #f1f3f5;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: #f8f9fa;
  border-color: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.stat-item.highlight {
  background: #e9ecef;
  font-weight: 600;
  border-left: 4px solid #0d6efd;
}

/* Transaction type indicators */
.transaction-type {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.transaction-type.airtime {
  background-color: #cff4fc;
  color: #055160;
}

.transaction-type.data {
  background-color: #d1e7dd;
  color: #0f5132;
}

.stat-label {
  color: #495057;
  font-size: 1rem;
  font-weight: 500;
}

.stat-value {
  font-weight: 700;
  font-size: 1.1rem;
  color: #2c3e50;
  padding: 0.35rem 0.75rem;
  background-color: #f8f9fa;
  border-radius: 0.5rem;
}

.download-card {
  background: #f8f9fa;
  border-radius: 1rem;
  padding: 2rem;
  border: 1px solid #e9ecef;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.download-buttons {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
  justify-content: center;
}

.download-button {
  flex: 1;
  min-width: 300px;
  padding: 1.25rem;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  border: 2px solid #0d6efd;
}

.download-button:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(13, 110, 253, 0.2);
  background-color: #0d6efd;
  color: #ffffff;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .stats-section {
    margin-bottom: 2rem;
  }

  .stat-card {
    margin-bottom: 1.5rem;
  }

  .download-button {
    width: 100%;
    min-width: 100%;
  }

  .stat-item {
    padding: 0.75rem 1rem;
  }

  .stat-value {
    font-size: 1rem;
    padding: 0.25rem 0.5rem;
  }
}

/* Status Colors */
.text-success {
  color: #198754 !important;
}

.text-warning {
  color: #ffc107 !important;
}

.text-danger {
  color: #dc3545 !important;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .form-control-custom {
    background-color: #2d2d2d;
    border-color: #3d3d3d;
    color: #ffffff;
  }

  .form-control-custom::placeholder {
    color: #6c757d;
  }

  .dark-theme .stat-card {
    background-color: #2a2a2a;
    border-color: #3d3d3d;
  }

  .dark-theme .stat-card h4 {
    color: #ffffff;
    border-bottom-color: #444;
  }

  .dark-theme .download-links {
    background-color: #2a2a2a;
    border-color: #3d3d3d;
  }

  .dark-theme .download-links h4 {
    color: #ffffff;
    border-bottom-color: #444;
  }
}

/* Adjust container padding */
.container {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

@media (min-width: 768px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

/* Professional UI Enhancements */

/* Smooth transitions for all interactive elements */
.btn,
.card,
.form-control,
.form-select {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced button hover effects */
.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn:active {
  transform: translateY(0);
  transition: all 0.1s ease;
}

/* Card hover effects */
.card {
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Form control enhancements */
.form-control:focus,
.form-select:focus {
  border-color: #42a5f5;
  box-shadow: 0 0 0 0.25rem rgba(66, 165, 245, 0.15);
  transform: translateY(-1px);
}

/* Icon animations */
.bi {
  transition: all 0.3s ease;
}

.btn:hover .bi {
  transform: scale(1.1);
}

/* Loading spinner enhancement */
.spinner-border {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Professional gradient overlays */
.gradient-overlay {
  position: relative;
  overflow: hidden;
}

.gradient-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.gradient-overlay:hover::before {
  left: 100%;
}

/* Enhanced table styling */
.table {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.table thead th {
  background: linear-gradient(135deg, #42a5f5 0%, #1e88e5 50%, #1565c0 100%);
  color: white;
  border: none;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 0.5px;
}

.table tbody tr {
  transition: all 0.3s ease;
}

.table tbody tr:hover {
  background-color: rgba(66, 165, 245, 0.05);
  transform: scale(1.01);
}

/* Professional badge styling */
.badge {
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
}

/* Enhanced modal styling */
.modal-content {
  border: none;
  border-radius: 1rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
}

.modal-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem 1rem 0 0;
}

.modal-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0 0 1rem 1rem;
}

/* Professional alert styling */
.alert {
  border: none;
  border-radius: 0.75rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
}

/* Smooth page transitions */
.page-transition {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Professional scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #42a5f5 0%, #1e88e5 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%);
}

/* Text selection styling */
::selection {
  background: rgba(66, 165, 245, 0.3);
  color: #1565c0;
}

/* Focus outline enhancement */
*:focus {
  outline: 2px solid rgba(66, 165, 245, 0.5);
  outline-offset: 2px;
}

/* Enhanced form styling */
.form-group-enhanced {
  position: relative;
  z-index: 1;
}

.form-control-custom:focus,
.form-select:focus {
  border-color: #1565c0 !important;
  box-shadow: 0 0 0 0.25rem rgba(66, 165, 245, 0.25) !important;
  transform: translateY(-2px);
  background-color: rgba(255, 255, 255, 1) !important;
}

.form-control-custom:hover:not(:disabled),
.form-select:hover:not(:disabled) {
  border-color: #1565c0;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(66, 165, 245, 0.2);
}

/* Enhanced button hover effects */
.btn-primary:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 25px rgba(66, 165, 245, 0.4) !important;
}

.btn-primary:active {
  transform: translateY(-1px) !important;
}

/* Card body enhancements */
.card .card-body {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
}

/* Form label enhancements */
.form-label {
  font-weight: 600;
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .form-container {
    padding: 1rem;
  }

  .card-body {
    padding: 1.5rem !important;
  }

  .btn-lg {
    height: 3rem !important;
    font-size: 1rem !important;
  }
}