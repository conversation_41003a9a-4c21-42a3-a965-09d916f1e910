import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON>, Spinner, Container, Row, Col, Table, Alert } from 'react-bootstrap';
import { useParams, useNavigate } from 'react-router-dom';

interface UserConsumption {
    target_date: string;
    telco: string;
    hisa_source: string;
    total_users: number;
    user_consumption: Array<{
        user_id: string;
        transaction_count: number;
        total_value_naira: number;
        successful_transactions: number;
        failed_transactions: number;
        success_rate: number;
    }>;
}

interface UserDetails {
    id: string;
    name: string;
    email: string;
}

interface Props {
    baseURL: string;
    token: string;
}

const UserConsumptionDetails: React.FC<Props> = ({ baseURL, token }) => {
    const { targetDate, telco, hisaSource } = useParams<{
        targetDate: string;
        telco: string;
        hisaSource: string;
    }>();
    const navigate = useNavigate();

    const [userConsumption, setUserConsumption] = useState<UserConsumption | null>(null);
    const [userDetails, setUserDetails] = useState<{ [key: string]: UserDetails }>({});
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (targetDate && telco && hisaSource) {
            fetchUserConsumption();
            fetchUserDetails();
        }
    }, [targetDate, telco, hisaSource]);

    const fetchUserConsumption = async () => {
        try {
            setLoading(true);
            setError(null);

            const apiBaseURL = baseURL.replace(/\/+$/, '');
            const authToken = token || localStorage.getItem('access_token') || localStorage.getItem('authToken');

            const response = await fetch(
                `${apiBaseURL}/telco-user-consumption/${targetDate}/${telco}?hisa_source=${hisaSource}`,
                {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                    },
                }
            );

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.status) {
                setUserConsumption(data.data);
            } else {
                setError(data.error || 'Failed to fetch user consumption data');
            }
        } catch (err) {
            console.error('Error fetching user consumption:', err);
            setError('Failed to fetch user consumption data. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const fetchUserDetails = async () => {
        try {
            const apiBaseURL = baseURL.replace(/\/+$/, '');
            const authToken = token || localStorage.getItem('access_token') || localStorage.getItem('authToken');

            const response = await fetch(`${apiBaseURL}/user-details`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                },
            });

            const data = await response.json();

            if (data.status && data.data) {
                setUserDetails(data.data);
            } else {
                console.warn('No user details available:', data.error || 'Unknown error');
            }
        } catch (error) {
            console.error('Error fetching user details:', error);
            // Continue without user details if API is not available
        }
    };

    const getStatusBadgeVariant = (successRate: number) => {
        if (successRate >= 90) return 'success';
        if (successRate >= 70) return 'warning';
        return 'danger';
    };

    if (loading) {
        return (
            <div className="form-container">
                <Container fluid className="px-4">
                    <div className="text-center py-5">
                        <Spinner animation="border" variant="primary" style={{ width: '3rem', height: '3rem' }} />
                        <h5 className="mt-3 text-muted">Loading user consumption details...</h5>
                    </div>
                </Container>
            </div>
        );
    }

    if (error) {
        return (
            <div className="form-container">
                <Container fluid className="px-4">
                    <Alert variant="danger" className="text-center">
                        <i className="bi bi-exclamation-triangle fs-1 d-block mb-3"></i>
                        <h5>{error}</h5>
                        <Button variant="outline-danger" onClick={() => navigate(-1)} className="mt-3">
                            <i className="bi bi-arrow-left me-2"></i>
                            Go Back
                        </Button>
                    </Alert>
                </Container>
            </div>
        );
    }

    if (!userConsumption) {
        return (
            <div className="form-container">
                <Container fluid className="px-4">
                    <Alert variant="info" className="text-center">
                        <i className="bi bi-info-circle fs-1 d-block mb-3"></i>
                        <h5>No data available</h5>
                        <Button variant="outline-info" onClick={() => navigate(-1)} className="mt-3">
                            <i className="bi bi-arrow-left me-2"></i>
                            Go Back
                        </Button>
                    </Alert>
                </Container>
            </div>
        );
    }

    return (
        <div className="form-container page-transition">
            <Container fluid className="px-4">
                {/* Header Section */}
                <Row className="mb-4">
                    <Col>
                        <Card className="shadow-lg border-0 rounded-4">
                            <Card.Header
                                className="text-white p-5 rounded-top-4 border-0 text-center"
                                style={{
                                    background: 'linear-gradient(135deg, #42a5f5 0%, #1e88e5 50%, #1565c0 100%)',
                                    position: 'relative'
                                }}
                            >
                                <div className="position-relative">
                                    <div className="icon-circle bg-white bg-opacity-20 mx-auto mb-3 d-flex align-items-center justify-content-center"
                                        style={{ width: '80px', height: '80px', borderRadius: '50%' }}>
                                        <i className="bi bi-people fs-1 text-white"></i>
                                    </div>
                                    <h2 className="fw-bold mb-2">User Consumption Details</h2>
                                    <p className="mb-3 fs-5 opacity-90">
                                        {userConsumption.telco} • {userConsumption.target_date} • {userConsumption.hisa_source.toUpperCase()}
                                    </p>
                                    <Button
                                        variant="outline-light"
                                        onClick={() => navigate(-1)}
                                        className="rounded-pill px-4 py-2"
                                        size="lg"
                                    >
                                        <i className="bi bi-arrow-left me-2"></i>
                                        Back to Summary
                                    </Button>
                                </div>
                            </Card.Header>
                        </Card>
                    </Col>
                </Row>

                {/* Statistics Cards */}
                <Row className="mb-4 g-4">
                    <Col xl={3} lg={6} md={6}>
                        <Card className="h-100 shadow-sm border-0 rounded-3 hover-card">
                            <Card.Body className="p-4">
                                <div className="d-flex align-items-center">
                                    <div className="icon-circle bg-primary bg-opacity-10 me-3">
                                        <i className="bi bi-people fs-2 text-primary"></i>
                                    </div>
                                    <div>
                                        <h6 className="text-muted mb-1 fw-semibold">Total Users</h6>
                                        <h4 className="mb-0 fw-bold text-primary">{userConsumption.total_users}</h4>
                                    </div>
                                </div>
                            </Card.Body>
                        </Card>
                    </Col>
                    <Col xl={3} lg={6} md={6}>
                        <Card className="h-100 shadow-sm border-0 rounded-3 hover-card">
                            <Card.Body className="p-4">
                                <div className="d-flex align-items-center">
                                    <div className="icon-circle bg-success bg-opacity-10 me-3">
                                        <i className="bi bi-graph-up fs-2 text-success"></i>
                                    </div>
                                    <div>
                                        <h6 className="text-muted mb-1 fw-semibold">Total Transactions</h6>
                                        <h4 className="mb-0 fw-bold text-success">
                                            {userConsumption.user_consumption.reduce((sum, user) => sum + user.transaction_count, 0).toLocaleString()}
                                        </h4>
                                    </div>
                                </div>
                            </Card.Body>
                        </Card>
                    </Col>
                    <Col xl={3} lg={6} md={6}>
                        <Card className="h-100 shadow-sm border-0 rounded-3 hover-card">
                            <Card.Body className="p-4">
                                <div className="d-flex align-items-center">
                                    <div className="icon-circle bg-info bg-opacity-10 me-3">
                                        <i className="bi bi-currency-exchange fs-2 text-info"></i>
                                    </div>
                                    <div>
                                        <h6 className="text-muted mb-1 fw-semibold">Total Value</h6>
                                        <h4 className="mb-0 fw-bold text-info">
                                            ₦{userConsumption.user_consumption.reduce((sum, user) => sum + user.total_value_naira, 0).toLocaleString()}
                                        </h4>
                                    </div>
                                </div>
                            </Card.Body>
                        </Card>
                    </Col>
                    <Col xl={3} lg={6} md={6}>
                        <Card className="h-100 shadow-sm border-0 rounded-3 hover-card">
                            <Card.Body className="p-4">
                                <div className="d-flex align-items-center">
                                    <div className="icon-circle bg-warning bg-opacity-10 me-3">
                                        <i className="bi bi-percent fs-2 text-warning"></i>
                                    </div>
                                    <div>
                                        <h6 className="text-muted mb-1 fw-semibold">Avg Success Rate</h6>
                                        <h4 className="mb-0 fw-bold text-warning">
                                            {userConsumption.user_consumption.length > 0
                                                ? (userConsumption.user_consumption.reduce((sum, user) => sum + user.success_rate, 0) / userConsumption.user_consumption.length).toFixed(1)
                                                : 0}%
                                        </h4>
                                    </div>
                                </div>
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>

                {/* User Details Table */}
                <Card className="shadow-lg border-0 rounded-4">
                    <Card.Header
                        className="text-white border-0 p-4"
                        style={{
                            background: 'linear-gradient(135deg, #42a5f5 0%, #1e88e5 50%, #1565c0 100%)',
                            borderRadius: '1rem 1rem 0 0'
                        }}
                    >
                        <h5 className="mb-1 fw-bold">
                            <i className="bi bi-table me-2"></i>
                            Individual User Consumption
                        </h5>
                        <small className="opacity-90">{userConsumption.user_consumption.length} users with transaction activity</small>
                    </Card.Header>
                    <Card.Body className="p-0">
                        {userConsumption.user_consumption.length === 0 ? (
                            <div className="text-center py-5">
                                <i className="bi bi-inbox fs-1 text-muted mb-3 d-block"></i>
                                <h5 className="text-muted">No user consumption data</h5>
                                <p className="text-muted mb-0">No users had transactions for {userConsumption.telco} on {userConsumption.target_date}</p>
                            </div>
                        ) : (
                            <Table hover responsive className="mb-0">
                                <thead className="bg-light">
                                    <tr>
                                        <th className="border-0 fw-semibold text-dark">User</th>
                                        <th className="border-0 fw-semibold text-dark">Email</th>
                                        <th className="border-0 fw-semibold text-dark">User ID</th>
                                        <th className="border-0 fw-semibold text-dark text-end">Transactions</th>
                                        <th className="border-0 fw-semibold text-dark text-end">Total Value</th>
                                        <th className="border-0 fw-semibold text-dark text-center">Success Rate</th>
                                        <th className="border-0 fw-semibold text-dark text-center">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {userConsumption.user_consumption.map((user) => {
                                        const details = userDetails[user.user_id];
                                        return (
                                            <tr key={user.user_id}>
                                                <td className="fw-medium">{details?.name || 'Unknown User'}</td>
                                                <td>{details?.email || 'No email available'}</td>
                                                <td><Badge bg="secondary">{user.user_id}</Badge></td>
                                                <td className="text-end fw-semibold">{user.transaction_count.toLocaleString()}</td>
                                                <td className="text-end fw-semibold text-success">₦{user.total_value_naira.toLocaleString()}</td>
                                                <td className="text-center">
                                                    <Badge bg={getStatusBadgeVariant(user.success_rate)}>
                                                        {user.success_rate.toFixed(1)}%
                                                    </Badge>
                                                </td>
                                                <td className="text-center">
                                                    <div className="d-flex justify-content-center gap-1">
                                                        <Badge bg="success" className="small">
                                                            ✓ {user.successful_transactions}
                                                        </Badge>
                                                        <Badge bg="danger" className="small">
                                                            ✗ {user.failed_transactions}
                                                        </Badge>
                                                    </div>
                                                </td>
                                            </tr>
                                        );
                                    })}
                                </tbody>
                            </Table>
                        )}
                    </Card.Body>
                </Card>
            </Container>

            <style>{`
                .hover-card {
                    transition: all 0.3s ease;
                }
                .hover-card:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
                }
                .icon-circle {
                    width: 60px;
                    height: 60px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .form-container {
                    min-height: 100vh;
                    padding: 2rem 0;
                }
                @media (max-width: 768px) {
                    .icon-circle {
                        width: 50px;
                        height: 50px;
                    }
                }
            `}</style>
        </div>
    );
};

export default UserConsumptionDetails;
