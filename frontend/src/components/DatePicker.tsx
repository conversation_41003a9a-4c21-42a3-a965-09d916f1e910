import React from 'react';
import { Form } from 'react-bootstrap';

interface DatePickerProps {
  label?: string;
  value: string;
  onChange: (date: string) => void;
  required?: boolean;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  size?: 'sm' | 'lg';
  min?: string;
  max?: string;
}

const DatePicker: React.FC<DatePickerProps> = ({
  label,
  value,
  onChange,
  required = false,
  disabled = false,
  placeholder = "Select date",
  className = "",
  size,
  min,
  max
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  };

  return (
    <Form.Group className="mb-3">
      {label && (
        <Form.Label className="fw-semibold mb-2" style={{ color: '#1565c0' }}>
          <i className="bi bi-calendar3 me-2" style={{ color: '#42a5f5' }}></i>
          {label}
          {required && <span className="text-danger ms-1">*</span>}
        </Form.Label>
      )}
      <div className="position-relative" style={{
        padding: '0.25rem',
        borderRadius: '0.75rem',
        background: 'linear-gradient(135deg, rgba(66, 165, 245, 0.1) 0%, rgba(30, 136, 229, 0.05) 100%)',
        border: '1px solid rgba(66, 165, 245, 0.2)'
      }}>
        <Form.Control
          type="date"
          value={value}
          onChange={handleChange}
          required={required}
          disabled={disabled}
          placeholder={placeholder}
          className={`form-control-custom ${className}`}
          size={size}
          min={min}
          max={max}
          style={{
            height: size === 'lg' ? '3.5rem' : size === 'sm' ? '2.5rem' : '3rem',
            fontSize: size === 'lg' ? '1.1rem' : size === 'sm' ? '0.9rem' : '1rem',
            paddingLeft: '1rem',
            paddingRight: '1rem',
            borderRadius: '0.5rem',
            border: '2px solid #42a5f5',
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            color: '#1565c0',
            fontWeight: '500',
            transition: 'all 0.3s ease'
          }}
        />
        <style>{`
          .form-control-custom:focus {
            border-color: #1565c0 !important;
            box-shadow: 0 0 0 0.25rem rgba(66, 165, 245, 0.25) !important;
            transform: translateY(-2px);
            background-color: rgba(255, 255, 255, 1) !important;
          }
          .form-control-custom:hover:not(:disabled) {
            border-color: #1565c0;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(66, 165, 245, 0.2);
          }
          .form-control-custom:disabled {
            background-color: #f8f9fa;
            opacity: 0.7;
          }
          
          /* Custom date picker styling */
          .form-control-custom::-webkit-calendar-picker-indicator {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='%231565c0' d='M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM2 2a1 1 0 0 0-1 1v1h14V3a1 1 0 0 0-1-1H2zm13 3H1v9a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V5z'/%3e%3c/svg%3e");
            background-size: 1.4rem;
            padding: 0.6rem;
            margin-right: 0.5rem;
            cursor: pointer;
            opacity: 0.8;
            transition: all 0.3s ease;
            border-radius: 0.25rem;
          }
          
          .form-control-custom::-webkit-calendar-picker-indicator:hover {
            opacity: 1;
          }
          
          /* Firefox date picker styling */
          .form-control-custom::-moz-focus-inner {
            border: 0;
          }
          
          /* Edge/IE date picker styling */
          .form-control-custom::-ms-clear {
            display: none;
          }
          
          .form-control-custom::-ms-reveal {
            display: none;
          }
        `}</style>
      </div>

      {/* Helper text for better UX */}
      <Form.Text className="small" style={{ color: '#42a5f5' }}>
        <i className="bi bi-info-circle me-1"></i>
        Select a date to view data for that specific day
      </Form.Text>
    </Form.Group>
  );
};

export default DatePicker;
