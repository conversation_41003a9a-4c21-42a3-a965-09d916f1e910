import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Form, Button, Alert, Modal, Card, Toast, ToastContainer } from 'react-bootstrap';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import 'bootstrap/dist/css/bootstrap.min.css';
import axios from 'axios';
import './App.css';
import ReconciliationResults from './components/ReconciliationResults';
import WalletsDashboard from './components/WalletsDashboard';
import CheckLogsDashboard from './components/CheckLogsDashboard';
import TelcoSummaryDashboard from './components/TelcoSummaryDashboard';
import UserConsumptionDetails from './components/UserConsumptionDetails';

// Use the existing VITE_API_BASE_URL environment variable
const baseURL = import.meta.env.DEV
  ? 'http://localhost:8080'
  : import.meta.env.VITE_API_BASE_URL;

// Import ReconciliationResult from types.ts
import { ReconciliationResult } from './types';

function AppContent() {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [username, setUsername] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [authError, setAuthError] = useState<string>('');
  const [token, setToken] = useState<string>(() => {
    // Initialize token from localStorage if it exists
    return localStorage.getItem('authToken') || '';
  });

  const [selectedProvider, setSelectedProvider] = useState<string>('mtn');
  const [targetDate, setTargetDate] = useState<string>('');
  const [hisaFile, setHisaFile] = useState<File | null>(null);
  const [hisaFile2, setHisaFile2] = useState<File | null>(null);
  const [telcoFile, setTelcoFile] = useState<File | null>(null);
  const [telcoFile2, setTelcoFile2] = useState<File | null>(null);
  const [useTransactionId, setUseTransactionId] = useState<boolean>(false);
  const [result, setResult] = useState<ReconciliationResult | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [showErrorModal, setShowErrorModal] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [showSuccessToast, setShowSuccessToast] = useState<boolean>(false);

  // UI mode: 'choice' | 'reconcile-logs' | 'reconcile-wallets' | 'check-logs' | 'telco-summary'
  const [mode, setMode] = useState<'choice' | 'reconcile-logs' | 'reconcile-wallets' | 'check-logs' | 'telco-summary'>('choice');

  // Add Modal for displaying error messages
  const ErrorModal: React.FC = () => (
    <Modal show={showErrorModal} onHide={() => setShowErrorModal(false)}>
      <Modal.Header closeButton className="bg-danger text-white">
        <Modal.Title>
          <i className="bi bi-exclamation-triangle-fill me-2"></i>
          Form Validation Error
        </Modal.Title>
      </Modal.Header>
      <Modal.Body className="p-4">
        <div className="d-flex align-items-center mb-3">
          <div className="bg-danger text-white rounded-circle p-2 me-3">
            <i className="bi bi-x-lg"></i>
          </div>
          <div>
            <h5 className="mb-1">Please check your form</h5>
            <p className="text-muted mb-0">The following issues need to be resolved:</p>
          </div>
        </div>
        <div
          className="alert alert-danger"
          dangerouslySetInnerHTML={{ __html: errorMessage }}
        />
      </Modal.Body>
      <Modal.Footer>
        <Button variant="outline-secondary" onClick={() => setShowErrorModal(false)}>
          <i className="bi bi-x me-2"></i>
          Close
        </Button>
        <Button variant="danger" onClick={() => setShowErrorModal(false)}>
          <i className="bi bi-check2 me-2"></i>
          Fix Issues
        </Button>
      </Modal.Footer>
    </Modal>
  );

  // Success Toast component
  const SuccessToast: React.FC = () => (
    <ToastContainer position="top-end" className="p-3" style={{ zIndex: 1060 }}>
      <Toast
        show={showSuccessToast}
        onClose={() => setShowSuccessToast(false)}
        delay={5000}
        autohide
        bg="success"
      >
        <Toast.Header closeButton={false}>
          <i className="bi bi-check-circle-fill text-success me-2"></i>
          <strong className="me-auto">Success</strong>
          <button
            type="button"
            className="btn-close"
            onClick={() => setShowSuccessToast(false)}
            aria-label="Close"
          ></button>
        </Toast.Header>
        <Toast.Body className="text-white">
          <strong>All fields are valid!</strong> Processing your request...
        </Toast.Body>
      </Toast>
    </ToastContainer>
  );

  useEffect(() => {
    // Check if we have a token on component mount
    const storedToken = localStorage.getItem('authToken');
    if (storedToken) {
      setToken(storedToken);
      setIsAuthenticated(true);
      // Remove this line since we're not using a login modal
    }
  }, []);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setAuthError('');

    try {
      const formData = new FormData();
      formData.append('username', username);
      formData.append('password', password);

      const response = await axios.post(`${baseURL}/login/`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const newToken = response.data.data.access_token;
      console.log('Login successful, token received:', newToken);
      setToken(newToken);
      localStorage.setItem('authToken', newToken);
      setIsAuthenticated(true);
      // Remove this line since we're not using a login modal
    } catch (err) {
      console.error('Login error:', err);
      setAuthError('Invalid credentials. Please try again.');
    }
  };

  const handleLogout = () => {
    setToken('');
    localStorage.removeItem('authToken');
    setIsAuthenticated(false);
    // Remove this line since we're not using a login modal
  };

  // Choice screen after login
  const ChoiceScreen: React.FC = () => (
    <div className="form-container page-transition">
      <Container fluid className="px-4">
        <Card className="shadow-lg border-0 rounded-4">
          <Card.Header
            className="text-white p-4 rounded-top-4 border-0 d-flex justify-content-between align-items-center"
            style={{
              background: 'linear-gradient(135deg, #42a5f5 0%, #1e88e5 50%, #1565c0 100%)'
            }}
          >
            <div>
              <h3 className="mb-0 fw-bold">
                <i className="bi bi-house-door me-2"></i>
                Welcome to HISA Reconciliation Manager
              </h3>
              <p className="mb-0 mt-2 opacity-75 fw-light">Choose a service to get started</p>
            </div>
            <Button variant="outline-light" onClick={handleLogout} className="px-3 py-2 rounded-pill">
              <i className="bi bi-box-arrow-right me-2"></i>
              Logout
            </Button>
          </Card.Header>
          <Card.Body className="p-5">
            {/* Main Services Grid */}
            <Row className="g-4 mb-4">
              <Col xl={3} lg={4} md={6} sm={12}>
                <Card className="h-100 shadow-sm border-0 rounded-3 hover-card">
                  <Card.Body className="d-flex flex-column text-center p-4">
                    <div className="mb-3">
                      <div className="icon-circle bg-primary bg-opacity-10 mx-auto mb-3">
                        <i className="bi bi-file-earmark-check fs-1 text-primary"></i>
                      </div>
                    </div>
                    <Card.Title className="h5 fw-bold text-primary">Reconcile Reports</Card.Title>
                    <Card.Text className="flex-grow-1 text-muted small">
                      Upload HISA and Telco files to generate comprehensive reconciliation reports with detailed analysis.
                    </Card.Text>
                    <div className="mt-auto">
                      <Button
                        onClick={() => setMode('reconcile-logs')}
                        variant="primary"
                        className="w-100 rounded-pill fw-semibold"
                      >
                        <i className="bi bi-arrow-right-circle me-2"></i>
                        Start Reconciliation
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Col>

              <Col xl={3} lg={4} md={6} sm={12}>
                <Card className="h-100 shadow-sm border-0 rounded-3 hover-card">
                  <Card.Body className="d-flex flex-column text-center p-4">
                    <div className="mb-3">
                      <div className="icon-circle bg-success bg-opacity-10 mx-auto mb-3">
                        <i className="bi bi-wallet2 fs-1 text-success"></i>
                      </div>
                    </div>
                    <Card.Title className="h5 fw-bold text-success">Reconcile Wallets</Card.Title>
                    <Card.Text className="flex-grow-1 text-muted small">
                      View user balances, analyze wallet transactions, and reconcile individual user accounts.
                    </Card.Text>
                    <div className="mt-auto">
                      <Button
                        onClick={() => setMode('reconcile-wallets')}
                        variant="success"
                        className="w-100 rounded-pill fw-semibold"
                      >
                        <i className="bi bi-arrow-right-circle me-2"></i>
                        Manage Wallets
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Col>

              <Col xl={3} lg={4} md={6} sm={12}>
                <Card className="h-100 shadow-sm border-0 rounded-3 hover-card">
                  <Card.Body className="d-flex flex-column text-center p-4">
                    <div className="mb-3">
                      <div className="icon-circle bg-warning bg-opacity-10 mx-auto mb-3">
                        <i className="bi bi-search fs-1 text-warning"></i>
                      </div>
                    </div>
                    <Card.Title className="h5 fw-bold text-warning">Check Logs</Card.Title>
                    <Card.Text className="flex-grow-1 text-muted small">
                      Download and analyze user transaction logs with comprehensive balance reconciliation.
                    </Card.Text>
                    <div className="mt-auto">
                      <Button
                        onClick={() => setMode('check-logs')}
                        variant="warning"
                        className="w-100 rounded-pill fw-semibold"
                      >
                        <i className="bi bi-arrow-right-circle me-2"></i>
                        Analyze Logs
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Col>

              <Col xl={3} lg={4} md={6} sm={12}>
                <Card className="h-100 shadow-sm border-0 rounded-3 hover-card">
                  <Card.Body className="d-flex flex-column text-center p-4">
                    <div className="mb-3">
                      <div className="icon-circle bg-info bg-opacity-10 mx-auto mb-3">
                        <i className="bi bi-bar-chart fs-1 text-info"></i>
                      </div>
                    </div>
                    <Card.Title className="h5 fw-bold text-info">Telco Summary</Card.Title>
                    <Card.Text className="flex-grow-1 text-muted small">
                      View transaction summaries by telco and detailed user consumption analytics.
                    </Card.Text>
                    <div className="mt-auto">
                      <Button
                        onClick={() => setMode('telco-summary')}
                        variant="info"
                        className="w-100 rounded-pill fw-semibold"
                      >
                        <i className="bi bi-arrow-right-circle me-2"></i>
                        View Analytics
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            </Row>

            {/* Quick Stats or Additional Info */}
            <Row className="mt-4">
              <Col>
                <div className="bg-light rounded-3 p-4 text-center">
                  <Row className="g-3">
                    <Col md={3} sm={6}>
                      <div className="d-flex align-items-center justify-content-center">
                        <i className="bi bi-shield-check text-success me-2 fs-4"></i>
                        <div>
                          <div className="fw-bold text-success">Secure</div>
                          <small className="text-muted">End-to-end encryption</small>
                        </div>
                      </div>
                    </Col>
                    <Col md={3} sm={6}>
                      <div className="d-flex align-items-center justify-content-center">
                        <i className="bi bi-lightning text-warning me-2 fs-4"></i>
                        <div>
                          <div className="fw-bold text-warning">Fast</div>
                          <small className="text-muted">Real-time processing</small>
                        </div>
                      </div>
                    </Col>
                    <Col md={3} sm={6}>
                      <div className="d-flex align-items-center justify-content-center">
                        <i className="bi bi-graph-up text-primary me-2 fs-4"></i>
                        <div>
                          <div className="fw-bold text-primary">Accurate</div>
                          <small className="text-muted">Precise reconciliation</small>
                        </div>
                      </div>
                    </Col>
                    <Col md={3} sm={6}>
                      <div className="d-flex align-items-center justify-content-center">
                        <i className="bi bi-clock text-info me-2 fs-4"></i>
                        <div>
                          <div className="fw-bold text-info">24/7</div>
                          <small className="text-muted">Always available</small>
                        </div>
                      </div>
                    </Col>
                  </Row>
                </div>
              </Col>
            </Row>
          </Card.Body>
        </Card>
      </Container>

      <style>{`
        .hover-card {
          transition: all 0.3s ease;
          cursor: pointer;
        }
        .hover-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
        }
        .icon-circle {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .form-container {
          min-height: 100vh;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          padding: 2rem 0;
        }
        @media (max-width: 768px) {
          .form-container {
            padding: 1rem 0;
          }
          .icon-circle {
            width: 60px;
            height: 60px;
          }
        }
      `}</style>
    </div>
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Detailed validation with specific error messages
    const missingFields = [];

    if (!targetDate) {
      missingFields.push("Target Date");
    }

    if (!hisaFile) {
      missingFields.push("Hisa1 File");
    }

    if (!hisaFile2) {
      missingFields.push("Hisa2 File");
    }

    if (!telcoFile) {
      missingFields.push("Telco File");
    }

    if (missingFields.length > 0) {
      const errorMsg = `
        <ul class="mb-0 ps-3">
          ${missingFields.map(field => `<li><strong>${field}</strong> is required</li>`).join('')}
        </ul>
      `;
      setErrorMessage(errorMsg);
      setShowErrorModal(true);
      return;
    }

    // Show success toast when all fields are valid
    setShowSuccessToast(true);

    setLoading(true);
    setErrorMessage('');

    const formData = new FormData();
    formData.append('mno', selectedProvider);
    formData.append('target_date', targetDate);
    if (hisaFile) formData.append('hisa_file', hisaFile);
    if (hisaFile2) formData.append('hisa_file2', hisaFile2);
    if (telcoFile) formData.append('telco_file', telcoFile);
    if (telcoFile2) {
      formData.append('telco_file2', telcoFile2);
    }
    formData.append('use_transaction_id', useTransactionId.toString());

    try {
      const response = await axios.post(
        `${baseURL}/reconcile_logs/`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            'Authorization': `Bearer ${token}`
          },
        }
      );
      console.log('Reconciliation successful:', response.data);
      // Hide success toast when result is received
      setShowSuccessToast(false);
      setResult(response.data);
    } catch (err) {
      console.error('Reconciliation error:', err);
      // Hide success toast when an error occurs
      setShowSuccessToast(false);

      if (axios.isAxiosError(err)) {
        console.log('Error status:', err.response?.status);
        console.log('Error response:', err.response?.data);
      }

      if (axios.isAxiosError(err) && err.response?.status === 401) {
        handleLogout();
        setErrorMessage('Session expired. Please login again.');
        setShowErrorModal(true);
      } else if (axios.isAxiosError(err) && err.response?.status === 500) {
        const errorMsg = err.response.data.error || 'An unexpected error occurred';
        setErrorMessage(`
          <div class="d-flex align-items-center">
            <i class="bi bi-exclamation-circle-fill text-danger me-2 fs-5"></i>
            <strong>${errorMsg}</strong>
          </div>
        `);
        setShowErrorModal(true);
      } else {
        setErrorMessage(`
          <div class="d-flex align-items-center">
            <i class="bi bi-exclamation-circle-fill text-danger me-2 fs-5"></i>
            <strong>Error processing files. Please try again.</strong>
          </div>
          <div class="mt-2 text-muted">
            Check that your files are in the correct format and try again.
          </div>
        `);
        setShowErrorModal(true);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    setResult(null);
    setHisaFile(null);
    setHisaFile2(null);
    setTelcoFile(null);
    setTelcoFile2(null);
    setTargetDate('');
    setMode('choice');
  };

  useEffect(() => {
    console.log('Token changed:', token);
    console.log('Is authenticated:', isAuthenticated);
  }, [token, isAuthenticated]);

  if (!isAuthenticated) {
    return (
      <>
        <div className="form-container">
          <Container fluid className="px-4">
            <Row className="justify-content-center">
              <Col xl={5} lg={6} md={8} sm={10}>
                <Card className="shadow-lg border-0 rounded-4">
                  <Card.Header
                    className="text-white p-5 rounded-top-4 border-0 text-center"
                    style={{
                      background: 'linear-gradient(135deg, #42a5f5 0%, #1e88e5 50%, #1565c0 100%)'
                    }}
                  >
                    <div className="mb-4">
                      <div className="icon-circle bg-white bg-opacity-20 mx-auto mb-3">
                        <i className="bi bi-shield-lock fs-1 text-white"></i>
                      </div>
                    </div>
                    <h3 className="mb-0 fw-bold">
                      Welcome Back
                    </h3>
                    <p className="mb-0 mt-2 opacity-75 fw-light">Sign in to HISA Reconciliation Manager</p>
                  </Card.Header>
                  <Card.Body className="p-5">
                    <Form onSubmit={handleLogin}>
                      <div className="mb-4">
                        <Form.Group className="mb-4">
                          <Form.Label className="fw-semibold mb-2 text-dark">
                            <i className="bi bi-person me-2 text-primary"></i>
                            Username
                          </Form.Label>
                          <Form.Control
                            type="text"
                            value={username}
                            onChange={(e) => setUsername(e.target.value)}
                            required
                            className="form-control-custom"
                            placeholder="Enter your username"
                            style={{ height: '3.5rem', fontSize: '1.1rem' }}
                          />
                        </Form.Group>

                        <Form.Group className="mb-4">
                          <Form.Label className="fw-semibold mb-2 text-dark">
                            <i className="bi bi-lock me-2 text-primary"></i>
                            Password
                          </Form.Label>
                          <Form.Control
                            type="password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            required
                            className="form-control-custom"
                            placeholder="Enter your password"
                            style={{ height: '3.5rem', fontSize: '1.1rem' }}
                          />
                        </Form.Group>
                      </div>

                      {authError && (
                        <Alert variant="danger" className="mb-4 rounded-3">
                          <i className="bi bi-exclamation-triangle me-2"></i>
                          {authError}
                        </Alert>
                      )}

                      <div className="d-grid mt-4">
                        <Button
                          variant="primary"
                          type="submit"
                          size="lg"
                          className="submit-button rounded-pill fw-semibold"
                          style={{ height: '3.5rem', fontSize: '1.2rem' }}
                        >
                          <i className="bi bi-box-arrow-in-right me-2"></i>
                          Sign In to Dashboard
                        </Button>
                      </div>
                    </Form>

                    {/* Additional Info */}
                    <div className="text-center mt-4 pt-4 border-top">
                      <Row className="g-3">
                        <Col sm={4}>
                          <div className="d-flex align-items-center justify-content-center">
                            <i className="bi bi-shield-check text-success me-2"></i>
                            <small className="text-muted">Secure Login</small>
                          </div>
                        </Col>
                        <Col sm={4}>
                          <div className="d-flex align-items-center justify-content-center">
                            <i className="bi bi-clock text-info me-2"></i>
                            <small className="text-muted">24/7 Access</small>
                          </div>
                        </Col>
                        <Col sm={4}>
                          <div className="d-flex align-items-center justify-content-center">
                            <i className="bi bi-headset text-warning me-2"></i>
                            <small className="text-muted">Support Ready</small>
                          </div>
                        </Col>
                      </Row>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          </Container>
        </div>

        <style>{`
          .icon-circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .form-control-custom:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
          }
          .submit-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(13, 110, 253, 0.3);
          }
          @media (max-width: 768px) {
            .icon-circle {
              width: 60px;
              height: 60px;
            }
          }
        `}</style>
      </>
    );
  }

  if (result) {
    return (
      <>
        <div className="form-container">
          <ReconciliationResults result={result} onBack={handleBack} />
        </div>
      </>
    );
  }

  return (
    <>
      <ErrorModal />
      <SuccessToast />

      {mode === 'choice' && <ChoiceScreen />}

      {mode === 'reconcile-logs' && (
        <div className="form-container">
          <Container>
            <Card className="shadow-lg border-0 rounded-4">
              <Card.Header
                className="text-white p-4 rounded-top-4 border-0 d-flex justify-content-between align-items-center"
                style={{
                  background: 'linear-gradient(135deg, #42a5f5 0%, #1e88e5 50%, #1565c0 100%)'
                }}
              >
                <div>
                  <h4 className="mb-0">HISA Logs Reconciliation</h4>
                </div>
                <div className="d-flex gap-2">
                  <Button variant="outline-light" onClick={() => setMode('choice')}>
                    Back
                  </Button>
                  <Button variant="outline-light" onClick={handleLogout} className="px-3 py-2">
                    <i className="bi bi-box-arrow-right me-2"></i>
                    Logout
                  </Button>
                </div>
              </Card.Header>

              <Card.Body className="p-4">
                <Form onSubmit={handleSubmit} className="reconciliation-form">
                  {/* Basic Information Section */}
                  <div className="form-section mb-4">
                    <h5 className="section-title mb-4">Basic Information</h5>
                    <div className="input-group-custom">
                      <Form.Group className="mb-4">
                        <Form.Label className="fw-medium mb-2">Provider</Form.Label>
                        <Form.Select
                          value={selectedProvider}
                          onChange={(e) => setSelectedProvider(e.target.value)}
                          required
                          className="form-select-custom"
                        >
                          <option value="mtn">MTN</option>
                          <option value="airtel">Airtel</option>
                          <option value="glo">GLO</option>
                        </Form.Select>
                      </Form.Group>

                      <Form.Group className="mb-4">
                        <Form.Label className="fw-medium mb-2">Target Date</Form.Label>
                        <Form.Control
                          type="date"
                          value={targetDate}
                          onChange={(e) => setTargetDate(e.target.value)}
                          required
                          className="form-control-custom"
                        />
                      </Form.Group>
                    </div>
                  </div>

                  {/* File Upload Section */}
                  <div className="form-section mb-4">
                    <h5 className="section-title mb-4">File Upload</h5>
                    <Row>
                      <Col md={6}>
                        <div className="file-upload-card">
                          <div className="file-upload-header mb-3">
                            <i className="bi bi-file-earmark-text file-icon"></i>
                            <h6 className="mb-0">Hisa1 File (Required)</h6>
                          </div>
                          <div className="upload-box">
                            <Form.Control
                              type="file"
                              onChange={(e) => setHisaFile((e.target as HTMLInputElement).files?.[0] || null)}
                              required
                              className="d-none"
                              id="hisaFile"
                            />
                            <label htmlFor="hisaFile" className="upload-label">
                              <i className="bi bi-cloud-upload upload-icon"></i>
                              <span className="upload-text">Click to upload or drag and drop</span>
                              {hisaFile && <span className="selected-file">{hisaFile.name}</span>}
                            </label>
                          </div>
                        </div>
                        <div className="file-upload-card mt-3">
                          <div className="file-upload-header mb-3">
                            <i className="bi bi-file-earmark-text file-icon"></i>
                            <h6 className="mb-0">Hisa2 File (Required)</h6>
                          </div>
                          <div className="upload-box">
                            <Form.Control
                              type="file"
                              onChange={(e) => setHisaFile2((e.target as HTMLInputElement).files?.[0] || null)}
                              required
                              className="d-none"
                              id="hisaFile2"
                            />
                            <label htmlFor="hisaFile2" className="upload-label">
                              <i className="bi bi-cloud-upload upload-icon"></i>
                              <span className="upload-text">Click to upload or drag and drop</span>
                              {hisaFile2 && <span className="selected-file">{hisaFile2.name}</span>}
                            </label>
                          </div>
                        </div>
                      </Col>
                      <Col md={6}>
                        <div className="file-upload-card">
                          <div className="file-upload-header mb-3">
                            <i className="bi bi-file-earmark-text file-icon"></i>
                            <h6 className="mb-0">Telco File (Required)</h6>
                          </div>
                          <div className="upload-box">
                            <Form.Control
                              type="file"
                              onChange={(e) => setTelcoFile((e.target as HTMLInputElement).files?.[0] || null)}
                              required
                              className="d-none"
                              id="telcoFile"
                            />
                            <label htmlFor="telcoFile" className="upload-label">
                              <i className="bi bi-cloud-upload upload-icon"></i>
                              <span className="upload-text">Click to upload or drag and drop</span>
                              {telcoFile && <span className="selected-file">{telcoFile.name}</span>}
                            </label>
                          </div>
                        </div>
                        <div className="file-upload-card mt-3">
                          <div className="file-upload-header mb-3">
                            <i className="bi bi-file-earmark-text file-icon"></i>
                            <h6 className="mb-0">Additional Telco File (Optional)</h6>
                          </div>
                          <div className="upload-box">
                            <Form.Control
                              type="file"
                              onChange={(e) => setTelcoFile2((e.target as HTMLInputElement).files?.[0] || null)}
                              className="d-none"
                              id="telcoFile2"
                            />
                            <label htmlFor="telcoFile2" className="upload-label">
                              <i className="bi bi-cloud-upload upload-icon"></i>
                              <span className="upload-text">Click to upload or drag and drop</span>
                              {telcoFile2 && <span className="selected-file">{telcoFile2.name}</span>}
                            </label>
                          </div>
                        </div>
                      </Col>
                    </Row>
                  </div>

                  {/* Options Section */}
                  <div className="form-section mb-4">
                    <h5 className="section-title mb-4">Additional Options</h5>
                    <div className="options-card">
                      <Form.Check
                        type="checkbox"
                        id="transactionIdCheck"
                        checked={useTransactionId}
                        onChange={(e) => setUseTransactionId(e.target.checked)}
                        label="Use Transaction ID for matching"
                        className="custom-checkbox"
                      />
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="d-grid mt-5">
                    <Button
                      variant="primary"
                      type="submit"
                      disabled={loading}
                      size="lg"
                      className="submit-button"
                    >
                      {loading ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                          Processing...
                        </>
                      ) : (
                        <>
                          <i className="bi bi-arrow-repeat me-2"></i>
                          Reconcile Transactions
                        </>
                      )}
                    </Button>
                  </div>
                </Form>
              </Card.Body>
            </Card>
          </Container>
        </div>
      )}

      {mode === 'reconcile-wallets' && (
        <div className="form-container">
          <Container>
            <Card className="shadow-lg border-0 rounded-4">
              <Card.Header className="bg-success bg-gradient text-white p-4 rounded-top-4 border-0 d-flex justify-content-between align-items-center">
                <div>
                  <h4 className="mb-0">HISA Wallets</h4>
                </div>
                <div className="d-flex gap-2">
                  <Button variant="outline-light" onClick={() => setMode('choice')}>
                    Back
                  </Button>
                  <Button variant="outline-light" onClick={handleLogout} className="px-3 py-2">
                    <i className="bi bi-box-arrow-right me-2"></i>
                    Logout
                  </Button>
                </div>
              </Card.Header>
              <Card.Body className="p-4">
                <WalletsDashboard baseURL={baseURL} token={token} />
              </Card.Body>
            </Card>
          </Container>
        </div>
      )}

      {mode === 'check-logs' && (
        <div className="form-container">
          <Container>
            <Card className="shadow-lg border-0 rounded-4">
              <Card.Header className="bg-warning bg-gradient text-dark p-4 rounded-top-4 border-0 d-flex justify-content-between align-items-center">
                <div>
                  <h4 className="mb-0">Check User Logs</h4>
                </div>
                <div className="d-flex gap-2">
                  <Button variant="outline-dark" onClick={() => setMode('choice')}>
                    Back
                  </Button>
                  <Button variant="outline-dark" onClick={handleLogout} className="px-3 py-2">
                    <i className="bi bi-box-arrow-right me-2"></i>
                    Logout
                  </Button>
                </div>
              </Card.Header>
              <Card.Body className="p-4">
                <CheckLogsDashboard baseURL={baseURL} token={token} />
              </Card.Body>
            </Card>
          </Container>
        </div>
      )}

      {mode === 'telco-summary' && (
        <div className="form-container">
          <Container>
            <Card className="shadow-lg border-0 rounded-4">
              <Card.Header className="bg-info bg-gradient text-white p-4 rounded-top-4 border-0 d-flex justify-content-between align-items-center">
                <div>
                  <h4 className="mb-0">Telco Transaction Summary</h4>
                </div>
                <div className="d-flex gap-2">
                  <Button variant="outline-light" onClick={() => setMode('choice')}>
                    Back
                  </Button>
                  <Button variant="outline-light" onClick={handleLogout} className="px-3 py-2">
                    <i className="bi bi-box-arrow-right me-2"></i>
                    Logout
                  </Button>
                </div>
              </Card.Header>
              <Card.Body className="p-4">
                <TelcoSummaryDashboard baseURL={baseURL} token={token} />
              </Card.Body>
            </Card>
          </Container>
        </div>
      )}
    </>
  );
}

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<AppContent />} />
        <Route path="/user-consumption/:targetDate/:telco/:hisaSource" element={<UserConsumptionDetailsWrapper />} />
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Router>
  );
}

// Wrapper component to pass props to UserConsumptionDetails
function UserConsumptionDetailsWrapper() {
  const baseURL = import.meta.env.DEV
    ? 'http://localhost:8080'
    : import.meta.env.VITE_API_BASE_URL;

  const token = localStorage.getItem('authToken') || '';

  return <UserConsumptionDetails baseURL={baseURL} token={token} />;
}

export default App;
