<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Routing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .card {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 12px 24px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-links {
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="card">
        <h1>🧪 Routing Test Page</h1>
        <p>This page helps test the React Router navigation for the HISA Reconciliation Manager.</p>
        
        <div class="test-links">
            <h3>Test Navigation Links:</h3>
            <a href="/#/" class="btn">🏠 Home</a>
            <a href="/#/user-consumption/2024-01-15/MTN/hisa_one" class="btn">👥 User Consumption (MTN)</a>
            <a href="/#/user-consumption/2024-01-15/AIRTEL/hisa_two" class="btn">👥 User Consumption (AIRTEL)</a>
            <a href="/#/user-consumption/2024-01-15/GLO/hisa_one" class="btn">👥 User Consumption (GLO)</a>
        </div>
        
        <div style="margin-top: 30px;">
            <h4>Instructions:</h4>
            <ol>
                <li>Start the React development server: <code>npm run dev</code></li>
                <li>Click on the test links above to verify routing works</li>
                <li>Check that the UserConsumptionDetails component loads correctly</li>
                <li>Verify the back navigation works properly</li>
            </ol>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: rgba(255, 255, 255, 0.1); border-radius: 10px;">
            <strong>Expected Behavior:</strong>
            <ul>
                <li>✅ Home link should show the main dashboard</li>
                <li>✅ User Consumption links should navigate to the details page</li>
                <li>✅ The details page should show a loading spinner initially</li>
                <li>✅ Back button should return to the previous page</li>
                <li>✅ All pages should have consistent gradient styling</li>
            </ul>
        </div>
    </div>
</body>
</html>
