"""
Telco Processing Strategy

Strategy pattern implementation for different telco processing requirements.
Follows Open/Closed Principle - open for extension, closed for modification.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any
import pandas as pd


class TelcoProcessingStrategy(ABC):
    """
    Abstract strategy for telco-specific processing.
    
    This enables different processing logic for different telcos
    while maintaining a consistent interface.
    """
    
    @abstractmethod
    def process_telco_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Process telco-specific data transformations.
        
        Args:
            df: Raw telco data
            
        Returns:
            pd.DataFrame: Processed telco data
        """
        pass
    
    @abstractmethod
    def validate_telco_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Validate telco-specific data requirements.
        
        Args:
            df: Telco data to validate
            
        Returns:
            dict: Validation result
        """
        pass
    
    @abstractmethod
    def get_match_key_columns(self, include_transaction_type: bool = False) -> list[str]:
        """
        Get columns used for matching transactions.
        
        Args:
            include_transaction_type: Whether to include transaction type in match key
            
        Returns:
            list: Column names for match key
        """
        pass


class MTNProcessingStrategy(TelcoProcessingStrategy):
    """
    MTN-specific processing strategy.
    
    MTN requires transaction type in match keys for proper reconciliation.
    """
    
    def process_telco_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process MTN-specific data transformations."""
        df = df.copy()
        
        # MTN-specific processing
        if "TransactionType" not in df.columns:
            df["TransactionType"] = "AIRTIME"  # Default for MTN
        
        # Ensure proper data types
        if "TxnId" in df.columns:
            df["TxnId"] = df["TxnId"].astype(str)
        if "MSISDN" in df.columns:
            df["MSISDN"] = df["MSISDN"].astype(str)
        
        return df
    
    def validate_telco_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Validate MTN-specific data requirements."""
        required_columns = ["TxnId", "MSISDN", "Amount", "TransactionType"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        return {
            "valid": len(missing_columns) == 0,
            "missing_columns": missing_columns,
            "telco": "MTN"
        }
    
    def get_match_key_columns(self, include_transaction_type: bool = True) -> list[str]:
        """Get MTN match key columns (includes transaction type by default)."""
        base_columns = ["TxnId", "MSISDN", "Amount"]
        if include_transaction_type:
            base_columns.append("TransactionType")
        return base_columns


class AirtelProcessingStrategy(TelcoProcessingStrategy):
    """
    Airtel-specific processing strategy.
    
    Airtel also requires transaction type in match keys.
    """
    
    def process_telco_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process Airtel-specific data transformations."""
        df = df.copy()
        
        # Airtel-specific processing
        if "TransactionType" not in df.columns:
            df["TransactionType"] = "AIRTIME"  # Default for Airtel
        
        # Ensure proper data types
        if "TxnId" in df.columns:
            df["TxnId"] = df["TxnId"].astype(str)
        if "MSISDN" in df.columns:
            df["MSISDN"] = df["MSISDN"].astype(str)
        
        return df
    
    def validate_telco_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Validate Airtel-specific data requirements."""
        required_columns = ["TxnId", "MSISDN", "Amount", "TransactionType"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        return {
            "valid": len(missing_columns) == 0,
            "missing_columns": missing_columns,
            "telco": "AIRTEL"
        }
    
    def get_match_key_columns(self, include_transaction_type: bool = True) -> list[str]:
        """Get Airtel match key columns (includes transaction type by default)."""
        base_columns = ["TxnId", "MSISDN", "Amount"]
        if include_transaction_type:
            base_columns.append("TransactionType")
        return base_columns


class GLOProcessingStrategy(TelcoProcessingStrategy):
    """
    GLO-specific processing strategy.
    
    GLO uses simpler match keys without transaction type.
    """
    
    def process_telco_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process GLO-specific data transformations."""
        df = df.copy()
        
        # GLO-specific processing
        if "TransactionType" not in df.columns:
            df["TransactionType"] = "AIRTIME"  # Default for GLO
        
        # Ensure proper data types
        if "TxnId" in df.columns:
            df["TxnId"] = df["TxnId"].astype(str)
        if "MSISDN" in df.columns:
            df["MSISDN"] = df["MSISDN"].astype(str)
        
        return df
    
    def validate_telco_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Validate GLO-specific data requirements."""
        required_columns = ["TxnId", "MSISDN", "Amount"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        return {
            "valid": len(missing_columns) == 0,
            "missing_columns": missing_columns,
            "telco": "GLO"
        }
    
    def get_match_key_columns(self, include_transaction_type: bool = False) -> list[str]:
        """Get GLO match key columns (excludes transaction type by default)."""
        base_columns = ["TxnId", "MSISDN", "Amount"]
        if include_transaction_type:
            base_columns.append("TransactionType")
        return base_columns


class TelcoProcessingStrategyFactory:
    """
    Factory for creating telco processing strategies.
    
    Follows Open/Closed Principle by allowing new strategies without modification.
    """
    
    _strategies = {
        "MTN": MTNProcessingStrategy,
        "AIRTEL": AirtelProcessingStrategy,
        "GLO": GLOProcessingStrategy,
    }
    
    @classmethod
    def create_strategy(cls, telco: str) -> TelcoProcessingStrategy:
        """
        Create appropriate processing strategy for telco.
        
        Args:
            telco: Telco name (case insensitive)
            
        Returns:
            TelcoProcessingStrategy: Appropriate strategy instance
            
        Raises:
            ValueError: If telco is not supported
        """
        telco_upper = telco.upper()
        if telco_upper not in cls._strategies:
            raise ValueError(f"Unsupported telco: {telco}. Supported telcos: {list(cls._strategies.keys())}")
        
        return cls._strategies[telco_upper]()
    
    @classmethod
    def register_strategy(cls, telco: str, strategy_class: type):
        """
        Register a new telco processing strategy.
        
        Args:
            telco: Telco name
            strategy_class: Strategy class to register
        """
        cls._strategies[telco.upper()] = strategy_class
    
    @classmethod
    def get_supported_telcos(cls) -> list[str]:
        """Get list of supported telcos."""
        return list(cls._strategies.keys())
