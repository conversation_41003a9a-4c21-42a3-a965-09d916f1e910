"""
File Processing Strategy

Strategy pattern implementation for different file processing requirements.
Follows Open/Closed Principle for different HISA sources.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any


class FileProcessingStrategy(ABC):
    """
    Abstract strategy for file processing operations.
    
    This enables different file processing logic for different HISA sources
    while maintaining a consistent interface.
    """
    
    @abstractmethod
    def get_transaction_file_path(self, target_date: str, hisa_source: str) -> str:
        """
        Get the transaction file path for a specific date and source.
        
        Args:
            target_date: Date in YYYY-MM-DD format
            hisa_source: HISA source identifier
            
        Returns:
            str: Full path to transaction file
        """
        pass
    
    @abstractmethod
    def get_wallet_file_path(self, target_date: str, hisa_source: str) -> str:
        """
        Get the wallet transaction file path for a specific date and source.
        
        Args:
            target_date: Date in YYYY-MM-DD format
            hisa_source: HISA source identifier
            
        Returns:
            str: Full path to wallet transaction file
        """
        pass
    
    @abstractmethod
    def get_expected_columns(self) -> list[str]:
        """
        Get expected columns for this file type.
        
        Returns:
            list: Expected column names
        """
        pass


class HisaOneFileStrategy(FileProcessingStrategy):
    """
    File processing strategy for HISA One.
    
    HISA One uses different file naming conventions.
    """
    
    def get_transaction_file_path(self, target_date: str, hisa_source: str) -> str:
        """Get HISA One transaction file path."""
        return f"reports/hisa_logs/{hisa_source}/{target_date}/{target_date}_airtime_transactions.csv"
    
    def get_wallet_file_path(self, target_date: str, hisa_source: str) -> str:
        """Get HISA One wallet transaction file path."""
        return f"reports/hisa_logs/{hisa_source}/{target_date}/{target_date}_wallet_transactions.csv"
    
    def get_expected_columns(self) -> list[str]:
        """Get expected columns for HISA One files."""
        return [
            "user_id",
            "amount", 
            "network",  # HISA One uses 'network' with numeric IDs
            "status",
            "type",
            "created_at"
        ]


class HisaTwoFileStrategy(FileProcessingStrategy):
    """
    File processing strategy for HISA Two.
    
    HISA Two uses different file naming conventions.
    """
    
    def get_transaction_file_path(self, target_date: str, hisa_source: str) -> str:
        """Get HISA Two transaction file path."""
        return f"reports/hisa_logs/{hisa_source}/{target_date}/{target_date}_transactions.csv"
    
    def get_wallet_file_path(self, target_date: str, hisa_source: str) -> str:
        """Get HISA Two wallet transaction file path."""
        return f"reports/hisa_logs/{hisa_source}/{target_date}/{target_date}_wallet_transactions.csv"
    
    def get_expected_columns(self) -> list[str]:
        """Get expected columns for HISA Two files."""
        return [
            "user_id",
            "amount",
            "network",  # HISA Two uses 'network' with string values
            "status", 
            "type",
            "created_at"
        ]


class FileProcessingStrategyFactory:
    """
    Factory for creating file processing strategies.
    
    Follows Open/Closed Principle by allowing new strategies without modification.
    """
    
    _strategies = {
        "hisa_one": HisaOneFileStrategy,
        "hisa_two": HisaTwoFileStrategy,
    }
    
    @classmethod
    def create_strategy(cls, hisa_source: str) -> FileProcessingStrategy:
        """
        Create appropriate file processing strategy for HISA source.
        
        Args:
            hisa_source: HISA source identifier
            
        Returns:
            FileProcessingStrategy: Appropriate strategy instance
            
        Raises:
            ValueError: If HISA source is not supported
        """
        if hisa_source not in cls._strategies:
            raise ValueError(f"Unsupported HISA source: {hisa_source}. Supported sources: {list(cls._strategies.keys())}")
        
        return cls._strategies[hisa_source]()
    
    @classmethod
    def register_strategy(cls, hisa_source: str, strategy_class: type):
        """
        Register a new file processing strategy.
        
        Args:
            hisa_source: HISA source identifier
            strategy_class: Strategy class to register
        """
        cls._strategies[hisa_source] = strategy_class
    
    @classmethod
    def get_supported_sources(cls) -> list[str]:
        """Get list of supported HISA sources."""
        return list(cls._strategies.keys())
