import os

from decouple import config
from pytz import timezone


# Add the application configuration(s) here.
BASE_DIR = os.getcwd()

ACCESS_TOKEN_EXPIRE_MINUTES = config("ACCESS_TOKEN_EXPIRE_MINUTES", cast=int)

API_AUTH = config("API_AUTH", cast=str)

DATABASE_URL = config("DATABASE_URL", cast=str)

ENVIRONMENT = config("ENVIRONMENT", cast=str)

REFRESH_TOKEN_EXPIRE_MINUTES = config("REFRESH_TOKEN_EXPIRE_MINUTES", cast=int)

SECRET_KEY = config("SECRET_KEY", cast=str)

TIMEZONE = timezone("Africa/Lagos")
