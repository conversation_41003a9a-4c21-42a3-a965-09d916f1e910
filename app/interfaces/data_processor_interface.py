"""
Data Processor Interface

Abstract base classes for data processing operations.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any
import pandas as pd


class TelcoDataProcessorInterface(ABC):
    """Interface for telco data processing operations."""
    
    @abstractmethod
    def process_transaction_data(self, file_path: str) -> pd.DataFrame:
        """Process transaction data from file."""
        pass
    
    @abstractmethod
    def calculate_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate summary statistics."""
        pass


class UserDataProcessorInterface(ABC):
    """Interface for user data processing operations."""
    
    @abstractmethod
    def process_transaction_data(self, file_path: str) -> pd.DataFrame:
        """Process transaction data from file."""
        pass
    
    @abstractmethod
    def calculate_user_consumption(self, df: pd.DataFrame, user_id: str) -> Dict[str, Any]:
        """Calculate user consumption statistics."""
        pass
