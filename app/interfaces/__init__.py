"""
Interfaces package for HISA Reconciliation Manager.

This package contains abstract base classes and interfaces that define
contracts for various services, following the Interface Segregation Principle.
"""

from .hisa_service_interface import HisaServiceInterface
from .sftp_service_interface import SFTPServiceInterface
from .data_processor_interface import (
    TelcoDataProcessorInterface,
    UserDataProcessorInterface,
)

__all__ = [
    "HisaServiceInterface",
    "SFTPServiceInterface",
    "TelcoDataProcessorInterface",
    "UserDataProcessorInterface",
]
