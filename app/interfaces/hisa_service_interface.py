"""
HISA Service Interface

Defines the contract for HISA wallet services.
Follows Interface Segregation Principle by defining specific interfaces.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any


class HisaServiceInterface(ABC):
    """
    Abstract interface for HISA wallet services.

    This interface defines the contract that all HISA service implementations
    must follow, enabling dependency inversion and easier testing.
    """

    @abstractmethod
    def fetch_users(self) -> Dict[str, Any]:
        """
        Fetch all users from the HISA service.

        Returns:
            dict: Response containing user data
        """
        pass

    @abstractmethod
    def fetch_wallets(self) -> Dict[str, Any]:
        """
        Fetch wallet information from the HISA service.

        Returns:
            dict: Response containing wallet data
        """
        pass

    @abstractmethod
    def get_user_balance(self, user_id: str) -> Dict[str, Any]:
        """
        Get balance for a specific user.

        Args:
            user_id: User identifier

        Returns:
            dict: Response containing user balance
        """
        pass

    @abstractmethod
    def get_wallet_history(
        self,
        user_id: str,
        from_date: str,
        to_date: str,
    ) -> Dict[str, Any]:
        """
        Get wallet transaction history for a user.

        Args:
            user_id: User identifier
            from_date: Start date for history
            to_date: End date for history

        Returns:
            dict: Response containing wallet history
        """
        pass

    @abstractmethod
    def get_monthly_balances(
        self,
        user_id: str,
        from_date: str,
        to_date: str,
    ) -> Dict[str, Any]:
        """
        Get monthly balance statements for a user.

        Args:
            user_id: User identifier
            from_date: Start date for statements
            to_date: End date for statements

        Returns:
            dict: Response containing monthly balances
        """
        pass


class HisaOneServiceInterface(HisaServiceInterface):
    """
    Specific interface for HISA One service operations.
    """

    pass


class HisaTwoServiceInterface(HisaServiceInterface):
    """
    Specific interface for HISA Two service operations.
    """

    pass
