"""
SFTP Service Interface

Defines the contract for SFTP operations.
Follows Interface Segregation Principle by defining specific SFTP operations.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any


class SFTPServiceInterface(ABC):
    """
    Abstract interface for SFTP operations.

    This interface defines the contract for SFTP services,
    enabling dependency inversion and easier testing.
    """

    @abstractmethod
    def connect(self) -> Any:
        """
        Establish SFTP connection.

        Returns:
            SFTP client connection

        Raises:
            ConnectionError: If connection fails
        """
        pass

    @abstractmethod
    def check_logs_exist_locally(self, target_date: str) -> Dict[str, Any]:
        """
        Check if logs exist locally for a given date.

        Args:
            target_date: Date to check logs for

        Returns:
            dict: Status of local log existence
        """
        pass

    @abstractmethod
    def download_logs(self, target_date: str) -> Dict[str, Any]:
        """
        Download logs for a specific date.

        Args:
            target_date: Date to download logs for

        Returns:
            dict: Download operation result
        """
        pass

    @abstractmethod
    def check_remote_directory_exists(self, directory_path: str) -> bool:
        """
        Check if a directory exists on the remote server.

        Args:
            directory_path: Path to check

        Returns:
            bool: True if directory exists, False otherwise
        """
        pass


class HisaOneSFTPInterface(SFTPServiceInterface):
    """
    Specific interface for HISA One SFTP operations.
    """

    pass


class HisaTwoSFTPInterface(SFTPServiceInterface):
    """
    Specific interface for HISA Two SFTP operations.
    """

    pass
