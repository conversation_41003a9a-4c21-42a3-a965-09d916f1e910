"""
Reconciliation Service

High-level service that orchestrates reconciliation operations.
Follows Interface Segregation and Liskov Substitution principles.
"""

from typing import Dict, Any, Protocol
import pandas as pd
from app.interfaces.hisa_service_interface import HisaServiceInterface
from app.interfaces.data_processor_interface import DataProcessorInterface
from app.strategies.telco_processing_strategy import TelcoProcessingStrategy
from app.strategies.file_processing_strategy import FileProcessingStrategy


class ReconciliationServiceInterface(Protocol):
    """
    Protocol defining the interface for reconciliation services.
    
    Follows Interface Segregation Principle by defining only essential methods.
    """
    
    def reconcile_transactions(
        self, 
        hisa_data: pd.DataFrame, 
        telco_data: pd.DataFrame,
        telco: str
    ) -> Dict[str, Any]:
        """Reconcile transactions between HISA and telco data."""
        ...
    
    def generate_reconciliation_report(
        self, 
        reconciliation_result: Dict[str, Any]
    ) -> str:
        """Generate reconciliation report from results."""
        ...


class TransactionReconciliationService:
    """
    Service for reconciling transaction data between HISA and telco sources.
    
    This service follows:
    - Single Responsibility: Focuses only on reconciliation logic
    - Dependency Inversion: Depends on abstractions, not concretions
    - Interface Segregation: Uses specific interfaces for different operations
    """
    
    def __init__(
        self,
        hisa_service: HisaServiceInterface,
        data_processor: DataProcessorInterface,
        telco_strategy: TelcoProcessingStrategy,
        file_strategy: FileProcessingStrategy
    ):
        """
        Initialize reconciliation service with dependencies.
        
        Args:
            hisa_service: HISA service implementation
            data_processor: Data processing implementation
            telco_strategy: Telco processing strategy
            file_strategy: File processing strategy
        """
        self._hisa_service = hisa_service
        self._data_processor = data_processor
        self._telco_strategy = telco_strategy
        self._file_strategy = file_strategy
    
    def reconcile_transactions(
        self, 
        hisa_data: pd.DataFrame, 
        telco_data: pd.DataFrame,
        telco: str
    ) -> Dict[str, Any]:
        """
        Reconcile transactions between HISA and telco data.
        
        Args:
            hisa_data: HISA transaction data
            telco_data: Telco transaction data
            telco: Telco name
            
        Returns:
            dict: Reconciliation results
        """
        # Validate data using the data processor
        hisa_validation = self._data_processor.validate_data(hisa_data)
        telco_validation = self._telco_strategy.validate_telco_data(telco_data)
        
        if not hisa_validation["valid"] or not telco_validation["valid"]:
            return {
                "status": "error",
                "hisa_validation": hisa_validation,
                "telco_validation": telco_validation
            }
        
        # Process data using strategies
        processed_hisa = self._data_processor.process_transaction_data("")  # Would need file path
        processed_telco = self._telco_strategy.process_telco_data(telco_data)
        
        # Calculate summaries
        hisa_summary = self._data_processor.calculate_summary(processed_hisa)
        telco_summary = self._data_processor.calculate_summary(processed_telco)
        
        # Perform reconciliation logic
        reconciliation_result = self._perform_reconciliation(
            processed_hisa, processed_telco, telco
        )
        
        return {
            "status": "success",
            "hisa_summary": hisa_summary,
            "telco_summary": telco_summary,
            "reconciliation": reconciliation_result
        }
    
    def generate_reconciliation_report(
        self, 
        reconciliation_result: Dict[str, Any]
    ) -> str:
        """
        Generate reconciliation report from results.
        
        Args:
            reconciliation_result: Results from reconciliation
            
        Returns:
            str: Path to generated report file
        """
        # Implementation would generate report file
        # This is a placeholder for the actual report generation logic
        return "reports/reconciliation_report.xlsx"
    
    def _perform_reconciliation(
        self, 
        hisa_data: pd.DataFrame, 
        telco_data: pd.DataFrame,
        telco: str
    ) -> Dict[str, Any]:
        """
        Internal method to perform actual reconciliation logic.
        
        Args:
            hisa_data: Processed HISA data
            telco_data: Processed telco data
            telco: Telco name
            
        Returns:
            dict: Reconciliation details
        """
        # Get match key columns from telco strategy
        match_columns = self._telco_strategy.get_match_key_columns()
        
        # Create match keys for both datasets
        hisa_keys = set()
        telco_keys = set()
        
        if all(col in hisa_data.columns for col in match_columns):
            hisa_data["match_key"] = hisa_data[match_columns].astype(str).agg("_".join, axis=1)
            hisa_keys = set(hisa_data["match_key"])
        
        if all(col in telco_data.columns for col in match_columns):
            telco_data["match_key"] = telco_data[match_columns].astype(str).agg("_".join, axis=1)
            telco_keys = set(telco_data["match_key"])
        
        # Find matches and mismatches
        matching_keys = hisa_keys & telco_keys
        missing_in_hisa = telco_keys - hisa_keys
        extra_in_hisa = hisa_keys - telco_keys
        
        return {
            "total_matches": len(matching_keys),
            "missing_in_hisa": len(missing_in_hisa),
            "extra_in_hisa": len(extra_in_hisa),
            "match_percentage": (len(matching_keys) / len(telco_keys) * 100) if telco_keys else 0
        }


class WalletReconciliationService:
    """
    Service for reconciling wallet data.
    
    Separate service following Interface Segregation Principle.
    """
    
    def __init__(self, hisa_service: HisaServiceInterface):
        """
        Initialize wallet reconciliation service.
        
        Args:
            hisa_service: HISA service implementation
        """
        self._hisa_service = hisa_service
    
    def reconcile_user_wallet(self, user_id: str, target_date: str) -> Dict[str, Any]:
        """
        Reconcile wallet for a specific user.
        
        Args:
            user_id: User identifier
            target_date: Target date for reconciliation
            
        Returns:
            dict: Wallet reconciliation results
        """
        # Get user balance
        balance_result = self._hisa_service.get_user_balance(user_id)
        
        # Get wallet history
        wallet_history = self._hisa_service.get_wallet_history(
            user_id, target_date, target_date
        )
        
        return {
            "user_id": user_id,
            "target_date": target_date,
            "current_balance": balance_result,
            "wallet_history": wallet_history
        }
