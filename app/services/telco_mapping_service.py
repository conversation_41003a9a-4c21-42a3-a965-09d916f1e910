"""
Telco Mapping Service

This service handles the mapping between different telco identifier formats
used by hisa_one (network IDs) and hisa_two (string values).

Follows Single Responsibility Principle by focusing solely on telco mapping logic.
"""

from typing import Dict, Optional, Union
import pandas as pd


class TelcoMappingService:
    """
    Service for handling telco network mapping between different HISA sources.
    
    This service abstracts the complexity of different network identifier formats:
    - HISA One uses numeric IDs (1=MTN, 2=AIRTEL, 4=GLO)
    - HISA Two uses string values ("MTN", "AIRTEL", "GLO")
    """
    
    # Standard telco mapping for both numeric IDs and string values
    TELCO_MAPPING = {
        # Numeric IDs (used by hisa_one)
        "1": "MTN",
        "2": "AIRTEL", 
        "4": "GLO",
        1: "MTN",
        2: "AIRTEL",
        4: "GLO",
        # String values (used by hisa_two and as fallback)
        "MTN": "MTN",
        "AIRTEL": "AIRTEL",
        "GLO": "<PERSON>L<PERSON>",
        "mtn": "MTN",
        "airtel": "AIRTEL", 
        "glo": "GLO",
    }
    
    # Reverse mapping for getting IDs from names
    TELCO_ID_MAPPING = {
        "MTN": 1,
        "AIRTEL": 2,
        "GLO": 4,
    }
    
    @classmethod
    def normalize_telco_name(cls, network_value: Union[str, int, float]) -> str:
        """
        Normalize any network identifier to standard telco name.
        
        Args:
            network_value: Network identifier (can be ID, string, or float)
            
        Returns:
            str: Standardized telco name (MTN, AIRTEL, GLO) or "UNKNOWN"
        """
        if pd.isna(network_value):
            return "UNKNOWN"
            
        # Convert to string and strip whitespace
        network_str = str(network_value).strip()
        
        # Handle float values that might come from pandas
        if network_str.endswith('.0'):
            network_str = network_str[:-2]
            
        return cls.TELCO_MAPPING.get(network_str, "UNKNOWN")
    
    @classmethod
    def get_telco_id(cls, telco_name: str) -> Optional[int]:
        """
        Get numeric ID for a telco name.
        
        Args:
            telco_name: Telco name (case insensitive)
            
        Returns:
            int: Numeric ID or None if not found
        """
        return cls.TELCO_ID_MAPPING.get(telco_name.upper())
    
    @classmethod
    def map_dataframe_network_column(cls, df: pd.DataFrame, network_column: str = "network") -> pd.DataFrame:
        """
        Add standardized telco column to dataframe based on network column.
        
        Args:
            df: DataFrame containing network information
            network_column: Name of the network column to map
            
        Returns:
            pd.DataFrame: DataFrame with added 'telco' column
            
        Raises:
            ValueError: If network column is not found
        """
        if network_column not in df.columns:
            raise ValueError(f"Network column '{network_column}' not found in dataframe")
            
        df = df.copy()
        df["telco"] = df[network_column].apply(cls.normalize_telco_name)
        return df
    
    @classmethod
    def validate_telco_name(cls, telco_name: str) -> bool:
        """
        Validate if a telco name is supported.
        
        Args:
            telco_name: Telco name to validate
            
        Returns:
            bool: True if telco is supported, False otherwise
        """
        return telco_name.upper() in cls.TELCO_ID_MAPPING
    
    @classmethod
    def get_supported_telcos(cls) -> list[str]:
        """
        Get list of supported telco names.
        
        Returns:
            list[str]: List of supported telco names
        """
        return list(cls.TELCO_ID_MAPPING.keys())
    
    @classmethod
    def filter_dataframe_by_telco(cls, df: pd.DataFrame, telco_name: str, network_column: str = "network") -> pd.DataFrame:
        """
        Filter dataframe by telco, handling both ID and string formats.
        
        Args:
            df: DataFrame to filter
            telco_name: Target telco name
            network_column: Name of the network column
            
        Returns:
            pd.DataFrame: Filtered dataframe
        """
        if network_column not in df.columns:
            return df
            
        telco_name = telco_name.upper()
        telco_id = cls.get_telco_id(telco_name)
        
        # Create filter condition for both string and ID formats
        df_copy = df.copy()
        df_copy[network_column] = df_copy[network_column].astype(str)
        
        condition = (
            (df_copy[network_column].str.upper() == telco_name) |
            (df_copy[network_column] == str(telco_id)) if telco_id else False
        )
        
        return df[condition]
