"""
HISA Wallet Service Implementation

Concrete implementations of HISA service interfaces.
Follows Single Responsibility and Dependency Inversion principles.
"""

from typing import Dict, Any
from app.interfaces.hisa_service_interface import (
    HisaOneServiceInterface,
    HisaTwoServiceInterface,
)
from app.config import API_AUTH
from app.utils import make_request


class HisaOneWalletService(HisaOneServiceInterface):
    """
    Concrete implementation for HISA One wallet operations.

    Follows Single Responsibility Principle by focusing only on HISA One operations.
    """

    def __init__(self):
        self.base_url = "https://api.myhisa.com/api/third-party/services/reports"
        self.headers = {"Accept": "application/json", "api-auth": API_AUTH}

    def fetch_users(self) -> Dict[str, Any]:
        """Fetch all users from HISA One service."""
        url = f"{self.base_url}/users"
        params = {"url": url, "headers": self.headers, "data": {}}
        return make_request("GET", params)

    def fetch_wallets(self) -> Dict[str, Any]:
        """Fetch wallet information from HISA One service."""
        url = f"{self.base_url}/wallets"
        params = {"url": url, "headers": self.headers, "data": {}}
        return make_request("GET", params)

    def get_user_balance(self, user_id: str) -> Dict[str, Any]:
        """Get balance for a specific user from HISA One."""
        url = f"{self.base_url}/balance?user_id={user_id}"
        params = {"url": url, "headers": self.headers, "data": {}}
        return make_request("GET", params)

    def get_wallet_history(
        self, user_id: str, from_date: str, to_date: str
    ) -> Dict[str, Any]:
        """Get wallet transaction history for a user from HISA One."""
        url = f"{self.base_url}/wallet-history?user_id={user_id}&from={from_date}&to={to_date}"
        params = {"url": url, "headers": self.headers, "data": {}}
        return make_request("GET", params)

    def get_monthly_balances(
        self, user_id: str, from_date: str, to_date: str
    ) -> Dict[str, Any]:
        """Get monthly balance statements for a user from HISA One."""
        url = f"{self.base_url}/statement/monthly?user_id={user_id}&from={from_date}&to={to_date}"
        params = {"url": url, "headers": self.headers, "data": {}}
        return make_request("GET", params)


class HisaTwoWalletService(HisaTwoServiceInterface):
    """
    Concrete implementation for HISA Two wallet operations.

    Follows Single Responsibility Principle by focusing only on HISA Two operations.
    """

    def __init__(self):
        self.base_url = "http://************:3000/api/third-party/services/reports"
        self.headers = {
            "Accept": "application/json",
            "Authorization": f"Bearer {API_AUTH}",
        }

    def fetch_users(self) -> Dict[str, Any]:
        """Fetch all users from HISA Two service."""
        url = f"{self.base_url}/users"
        params = {"url": url, "headers": self.headers, "data": {}}
        return make_request("GET", params)

    def fetch_wallets(self) -> Dict[str, Any]:
        """Fetch wallet information from HISA Two service."""
        url = f"{self.base_url}/wallets"
        params = {"url": url, "headers": self.headers, "data": {}}
        return make_request("GET", params)

    def get_user_balance(self, user_id: str) -> Dict[str, Any]:
        """Get balance for a specific user from HISA Two."""
        url = f"{self.base_url}/balance?user_id={user_id}"
        params = {"url": url, "headers": self.headers, "data": {}}
        return make_request("GET", params)

    def get_wallet_history(
        self, user_id: str, from_date: str, to_date: str
    ) -> Dict[str, Any]:
        """Get wallet transaction history for a user from HISA Two."""
        url = f"{self.base_url}/wallet-history?user_id={user_id}&from={from_date}&to={to_date}"
        params = {"url": url, "headers": self.headers, "data": {}}
        return make_request("GET", params)

    def get_monthly_balances(
        self, user_id: str, from_date: str, to_date: str
    ) -> Dict[str, Any]:
        """Get monthly balance statements for a user from HISA Two."""
        url = f"{self.base_url}/statement/monthly?user_id={user_id}&from_date={from_date}&to_date={to_date}"
        params = {"url": url, "headers": self.headers, "data": {}}
        return make_request("GET", params)


class HisaWalletServiceFactory:
    """
    Factory for creating HISA wallet services.

    Follows Open/Closed Principle by allowing extension without modification.
    """

    @staticmethod
    def create_service(hisa_type: str):
        """
        Create appropriate HISA service based on type.

        Args:
            hisa_type: Either "hisa_one" or "hisa_two"

        Returns:
            Appropriate HISA service instance

        Raises:
            ValueError: If invalid hisa_type provided
        """
        if hisa_type == "hisa_one":
            return HisaOneWalletService()
        elif hisa_type == "hisa_two":
            return HisaTwoWalletService()
        else:
            raise ValueError(
                f"Invalid HISA type: {hisa_type}. Must be 'hisa_one' or 'hisa_two'."
            )
