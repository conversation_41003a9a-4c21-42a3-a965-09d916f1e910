"""
Transaction Processor Service

Concrete implementation of data processing interfaces.
Follows Single Responsibility and Interface Segregation principles.
"""

from typing import Dict, Any
import pandas as pd
from app.interfaces.data_processor_interface import TelcoDataProcessorInterface, UserDataProcessorInterface
from app.services.data_processing_service import DataProcessingService
from app.services.telco_mapping_service import TelcoMappingService


class TelcoTransactionProcessor(TelcoDataProcessorInterface):
    """
    Processor for telco-specific transaction operations.
    
    Follows Single Responsibility Principle by focusing only on telco processing.
    """
    
    def process_transaction_data(self, file_path: str) -> pd.DataFrame:
        """Process transaction data from file."""
        try:
            df = pd.read_csv(file_path)
        except FileNotFoundError:
            raise FileNotFoundError(f"Transaction file not found: {file_path}")
        
        if df.empty:
            return df
        
        # Convert currency
        df = DataProcessingService.convert_kobo_to_naira(df)
        
        # Add telco mapping
        network_columns = ["network", "Network", "network_id"]
        network_column = None
        for col in network_columns:
            if col in df.columns:
                network_column = col
                break
        
        if network_column:
            df = TelcoMappingService.map_dataframe_network_column(df, network_column)
        
        return df
    
    def calculate_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate summary statistics for transaction data."""
        return DataProcessingService.calculate_transaction_summary(df)
    
    def validate_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Validate transaction data structure and content."""
        required_columns = ["amount"]
        return DataProcessingService.validate_required_columns(df, required_columns)
    
    def group_by_telco(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """Group transaction data by telco."""
        if "telco" not in df.columns or df.empty:
            return {}
        
        telco_groups = {}
        for telco in df["telco"].unique():
            if pd.notna(telco):
                telco_groups[str(telco)] = df[df["telco"] == telco]
        
        return telco_groups
    
    def calculate_telco_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate telco-specific summary statistics."""
        if df.empty:
            return {
                "total_transactions": 0,
                "total_value_naira": 0.0,
                "telco_breakdown": {},
                "status_breakdown": {},
                "type_breakdown": {}
            }
        
        # Overall summary
        overall_summary = self.calculate_summary(df)
        
        # Telco breakdown
        telco_groups = self.group_by_telco(df)
        telco_breakdown = {}
        for telco, telco_df in telco_groups.items():
            telco_breakdown[telco] = {
                **self.calculate_summary(telco_df),
                "by_status": DataProcessingService.group_by_status(telco_df),
                "by_type": DataProcessingService.group_by_type(telco_df)
            }
        
        return {
            **overall_summary,
            "telco_breakdown": telco_breakdown,
            "status_breakdown": DataProcessingService.group_by_status(df),
            "type_breakdown": DataProcessingService.group_by_type(df)
        }


class UserTransactionProcessor(UserDataProcessorInterface):
    """
    Processor for user-specific transaction operations.
    
    Follows Single Responsibility Principle by focusing only on user processing.
    """
    
    def process_transaction_data(self, file_path: str) -> pd.DataFrame:
        """Process transaction data from file."""
        try:
            df = pd.read_csv(file_path)
        except FileNotFoundError:
            raise FileNotFoundError(f"Transaction file not found: {file_path}")
        
        if df.empty:
            return df
        
        # Convert currency
        df = DataProcessingService.convert_kobo_to_naira(df)
        
        # Add telco mapping
        network_columns = ["network", "Network", "network_id"]
        network_column = None
        for col in network_columns:
            if col in df.columns:
                network_column = col
                break
        
        if network_column:
            df = TelcoMappingService.map_dataframe_network_column(df, network_column)
        
        return df
    
    def calculate_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate summary statistics for transaction data."""
        return DataProcessingService.calculate_transaction_summary(df)
    
    def validate_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Validate transaction data structure and content."""
        required_columns = ["amount", "user_id"]
        return DataProcessingService.validate_required_columns(df, required_columns)
    
    def group_by_user(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """Group transaction data by user."""
        if "user_id" not in df.columns or df.empty:
            return {}
        
        user_groups = {}
        for user_id in df["user_id"].unique():
            if pd.notna(user_id):
                user_groups[str(user_id)] = df[df["user_id"] == user_id]
        
        return user_groups
    
    def calculate_user_consumption(self, df: pd.DataFrame, user_id: str) -> Dict[str, Any]:
        """Calculate user consumption statistics."""
        if "user_id" not in df.columns:
            return {"error": "user_id column not found"}
        
        user_data = df[df["user_id"] == user_id]
        
        if user_data.empty:
            return {
                "user_id": user_id,
                "total_transactions": 0,
                "total_value_naira": 0.0,
                "by_status": {},
                "by_type": {}
            }
        
        return {
            "user_id": user_id,
            **self.calculate_summary(user_data),
            "by_status": DataProcessingService.group_by_status(user_data),
            "by_type": DataProcessingService.group_by_type(user_data)
        }
