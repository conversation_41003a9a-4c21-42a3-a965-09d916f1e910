"""
Data Processing Service

This service handles common data processing operations for transaction data.
Follows Single Responsibility Principle by focusing on data transformation logic.
"""

from typing import Dict, Any
import pandas as pd
from .telco_mapping_service import TelcoMappingService


class DataProcessingService:
    """
    Service for common data processing operations on transaction data.
    
    This service handles:
    - Currency conversion (Kobo to Naira)
    - Data validation and cleaning
    - Common aggregations and summaries
    """
    
    @staticmethod
    def convert_kobo_to_naira(df: pd.DataFrame, amount_column: str = "amount") -> pd.DataFrame:
        """
        Convert amount from Kobo to Naira.
        
        Args:
            df: DataFrame containing amount data
            amount_column: Name of the amount column
            
        Returns:
            pd.DataFrame: DataFrame with added amount_naira column
        """
        df = df.copy()
        if amount_column in df.columns:
            df["amount_naira"] = df[amount_column] / 100.0
        return df
    
    @staticmethod
    def validate_required_columns(df: pd.DataFrame, required_columns: list[str]) -> Dict[str, Any]:
        """
        Validate that required columns exist in dataframe.
        
        Args:
            df: DataFrame to validate
            required_columns: List of required column names
            
        Returns:
            dict: Validation result with status and missing columns
        """
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        return {
            "valid": len(missing_columns) == 0,
            "missing_columns": missing_columns,
            "message": f"Missing columns: {missing_columns}" if missing_columns else "All required columns present"
        }
    
    @staticmethod
    def calculate_transaction_summary(df: pd.DataFrame) -> Dict[str, Any]:
        """
        Calculate basic transaction summary statistics.
        
        Args:
            df: DataFrame with transaction data (must have amount_naira column)
            
        Returns:
            dict: Summary statistics
        """
        if df.empty:
            return {
                "total_transactions": 0,
                "total_value_naira": 0.0,
                "average_transaction_value": 0.0
            }
        
        return {
            "total_transactions": len(df),
            "total_value_naira": round(df["amount_naira"].sum(), 2) if "amount_naira" in df.columns else 0.0,
            "average_transaction_value": round(df["amount_naira"].mean(), 2) if "amount_naira" in df.columns else 0.0
        }
    
    @staticmethod
    def group_by_status(df: pd.DataFrame, status_column: str = "status") -> Dict[str, Dict[str, Any]]:
        """
        Group transactions by status and calculate summaries.
        
        Args:
            df: DataFrame with transaction data
            status_column: Name of the status column
            
        Returns:
            dict: Status-wise summaries
        """
        if status_column not in df.columns or df.empty:
            return {}
        
        status_summary = {}
        for status in df[status_column].unique():
            status_data = df[df[status_column] == status]
            status_summary[str(status)] = DataProcessingService.calculate_transaction_summary(status_data)
        
        return status_summary
    
    @staticmethod
    def group_by_type(df: pd.DataFrame, type_column: str = "type") -> Dict[str, Dict[str, Any]]:
        """
        Group transactions by type and calculate summaries.
        
        Args:
            df: DataFrame with transaction data
            type_column: Name of the type column
            
        Returns:
            dict: Type-wise summaries
        """
        if type_column not in df.columns or df.empty:
            return {}
        
        type_summary = {}
        for transaction_type in df[type_column].unique():
            type_data = df[df[type_column] == transaction_type]
            type_summary[str(transaction_type)] = DataProcessingService.calculate_transaction_summary(type_data)
        
        return type_summary
    
    @staticmethod
    def process_transaction_file(file_path: str, hisa_source: str) -> pd.DataFrame:
        """
        Process transaction file with proper telco mapping and currency conversion.
        
        Args:
            file_path: Path to the CSV file
            hisa_source: Source type ("hisa_one" or "hisa_two")
            
        Returns:
            pd.DataFrame: Processed dataframe
            
        Raises:
            FileNotFoundError: If file doesn't exist
            ValueError: If required columns are missing
        """
        try:
            df = pd.read_csv(file_path)
        except FileNotFoundError:
            raise FileNotFoundError(f"Transaction file not found: {file_path}")
        
        if df.empty:
            return df
        
        # Validate required columns
        required_columns = ["amount"]
        validation = DataProcessingService.validate_required_columns(df, required_columns)
        if not validation["valid"]:
            raise ValueError(f"Missing required columns: {validation['missing_columns']}")
        
        # Convert currency
        df = DataProcessingService.convert_kobo_to_naira(df)
        
        # Add telco mapping if network column exists
        network_columns = ["network", "Network", "network_id"]
        network_column = None
        for col in network_columns:
            if col in df.columns:
                network_column = col
                break
        
        if network_column:
            df = TelcoMappingService.map_dataframe_network_column(df, network_column)
        
        return df
